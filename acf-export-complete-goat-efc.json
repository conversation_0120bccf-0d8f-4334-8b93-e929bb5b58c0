[{"key": "group_goat_partners", "title": "Partner Fields", "fields": [{"key": "field_partner_logo", "label": "Partner Logo", "name": "partner_logo", "type": "image", "instructions": "Upload the partner's logo", "required": 1, "return_format": "array", "preview_size": "medium"}, {"key": "field_partner_website", "label": "Partner Website", "name": "partner_website", "type": "url", "instructions": "Enter the partner's website URL", "required": 0}, {"key": "field_partner_description", "label": "Partner Description", "name": "partner_description", "type": "textarea", "instructions": "Brief description of the partner", "required": 0, "rows": 4}], "location": [[{"param": "post_type", "operator": "==", "value": "partner"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_events", "title": "Event Fields", "fields": [{"key": "field_event_date", "label": "Event Date", "name": "event_date", "type": "date_picker", "instructions": "Select the event date", "required": 1, "display_format": "d/m/Y", "return_format": "Y-m-d"}, {"key": "field_event_location", "label": "Event Location", "name": "event_location", "type": "text", "instructions": "Enter the event location", "required": 0}, {"key": "field_event_description", "label": "Event Description", "name": "event_description", "type": "textarea", "instructions": "Brief description of the event", "required": 0, "rows": 4}, {"key": "field_event_images", "label": "Event Images", "name": "event_images", "type": "gallery", "instructions": "Upload multiple images for this event", "required": 0, "return_format": "array", "preview_size": "medium", "library": "all"}, {"key": "field_event_registration_link", "label": "Registration Link", "name": "event_registration_link", "type": "link", "instructions": "Add registration or ticket link", "required": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "event"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_team_members", "title": "Team Member <PERSON>", "fields": [{"key": "field_show_detail_page", "label": "Show Detail Page", "name": "show_detail_page", "type": "true_false", "instructions": "Enable this to allow visitors to view a detail page for this team member", "required": 0, "default_value": 1, "ui": 1}, {"key": "field_member_role", "label": "Role/Position", "name": "member_role", "type": "text", "instructions": "Enter the team member's role or position", "required": 0}, {"key": "field_member_bio", "label": "Bio", "name": "member_bio", "type": "textarea", "instructions": "Brief bio of the team member", "required": 0, "rows": 4}, {"key": "field_member_category", "label": "Member Category", "name": "member_category", "type": "select", "instructions": "Select the team member category", "required": 0, "choices": {"management": "Management", "players": "Players", "content": "Content Creators", "support": "Support Staff"}, "default_value": "players", "allow_null": 1}, {"key": "field_member_additional_images", "label": "Additional Images", "name": "member_additional_images", "type": "gallery", "instructions": "Upload additional images for this team member", "required": 0, "return_format": "array", "preview_size": "medium", "library": "all"}, {"key": "field_member_twitch", "label": "Twitch URL", "name": "member_twitch", "type": "url", "instructions": "Enter Twitch profile URL", "required": 0}, {"key": "field_member_tiktok", "label": "TikTok URL", "name": "member_tiktok", "type": "url", "instructions": "<PERSON><PERSON> Tik<PERSON>ok profile URL", "required": 0}, {"key": "field_member_instagram", "label": "Instagram URL", "name": "member_instagram", "type": "url", "instructions": "Enter Instagram profile URL", "required": 0}, {"key": "field_member_twitter", "label": "Twitter URL", "name": "member_twitter", "type": "url", "instructions": "Enter Twitter profile URL", "required": 0}, {"key": "field_member_discord", "label": "Discord URL", "name": "member_discord", "type": "url", "instructions": "Enter Discord profile URL", "required": 0}, {"key": "field_member_facebook", "label": "Facebook URL", "name": "member_facebook", "type": "url", "instructions": "Enter Facebook profile URL", "required": 0}, {"key": "field_member_youtube", "label": "YouTube URL", "name": "member_youtube", "type": "url", "instructions": "Enter YouTube channel URL", "required": 0}], "location": [[{"param": "post_type", "operator": "==", "value": "team_member"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_home_header_block", "title": "GOAT Home Header Block", "fields": [{"key": "field_home_header_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Optional anchor ID for navigation", "required": 0}, {"key": "field_home_header_background_image", "label": "Background Image", "name": "background_image", "type": "image", "instructions": "Upload background image", "required": 0, "return_format": "array", "preview_size": "medium"}, {"key": "field_home_header_background_video", "label": "Background Video", "name": "background_video", "type": "file", "instructions": "Upload background video (MP4 format)", "required": 0, "return_format": "array", "mime_types": "mp4"}, {"key": "field_home_header_logo", "label": "Logo", "name": "logo", "type": "image", "instructions": "Upload main logo", "required": 0, "return_format": "array", "preview_size": "medium"}, {"key": "field_home_header_title", "label": "Title", "name": "title", "type": "text", "instructions": "Main title text", "required": 0}, {"key": "field_home_header_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "instructions": "Subtitle text", "required": 0}, {"key": "field_home_header_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Description text", "required": 0, "rows": 4}, {"key": "field_home_header_cta_button", "label": "CTA Button", "name": "cta_button", "type": "link", "instructions": "Call to action button", "required": 0}, {"key": "field_home_header_scroll_indicator", "label": "Show Scroll Indicator", "name": "scroll_indicator", "type": "true_false", "instructions": "Show scroll down indicator", "required": 0, "default_value": 1, "ui": 1}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-home-header-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_home_intro_block", "title": "GOAT Home Intro Block", "fields": [{"key": "field_home_intro_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Optional anchor ID for navigation", "required": 0}, {"key": "field_home_intro_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "instructions": "Section subtitle", "required": 0}, {"key": "field_home_intro_title", "label": "Title", "name": "title", "type": "text", "instructions": "Section title", "required": 0}, {"key": "field_home_intro_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Section description", "required": 0, "rows": 4}, {"key": "field_home_intro_features", "label": "Features", "name": "features", "type": "repeater", "instructions": "Add feature items", "required": 0, "sub_fields": [{"key": "field_feature_icon", "label": "Feature Icon", "name": "icon", "type": "image", "required": 0, "return_format": "array", "preview_size": "thumbnail"}, {"key": "field_feature_title", "label": "Feature Title", "name": "title", "type": "text", "required": 1}, {"key": "field_feature_description", "label": "Feature Description", "name": "description", "type": "textarea", "required": 0, "rows": 3}], "min": 0, "max": 6, "layout": "row", "button_label": "Add Feature"}, {"key": "field_home_intro_side_image", "label": "Side Image", "name": "side_image", "type": "image", "instructions": "Optional side image", "required": 0, "return_format": "array", "preview_size": "medium"}, {"key": "field_home_intro_cta_button", "label": "CTA Button", "name": "cta_button", "type": "link", "instructions": "Call to action button", "required": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-home-intro-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_team_members_home_block", "title": "GOAT Team Members Home Block", "fields": [{"key": "field_team_home_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "required": 0}, {"key": "field_team_home_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "required": 0}, {"key": "field_team_home_title", "label": "Title", "name": "title", "type": "text", "required": 0}, {"key": "field_team_home_description", "label": "Description", "name": "description", "type": "textarea", "required": 0, "rows": 4}, {"key": "field_team_home_featured_members", "label": "Featured Members", "name": "featured_members", "type": "relationship", "instructions": "Select specific team members to feature (leave empty to show latest 6)", "required": 0, "post_type": ["team_member"], "min": 0, "max": 6, "return_format": "object"}, {"key": "field_team_home_view_all_button", "label": "View All Button", "name": "view_all_button", "type": "link", "required": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-team-members-home-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}]