<!DOCTYPE html>
<html lang="nl" dir="ltr">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <title><?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?></title>
  <meta name="robots" content="follow, index, max-snippet:-1, max-video-preview:-1, max-image-preview:large">
  <meta name="msapplication-TileColor" content="#00aba9">
  <meta name="theme-color" content="#ffffff">
  <meta name="description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="author" content="https://www.linkedin.com/in/dennisthemenace/"/>
  <meta property="og:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>" />
  <meta property="og:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>" />
  <meta property="og:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>" />
  <meta property="og:image:alt" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-title')); ?>" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:image:type" content="image/jpeg" />
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<?php echo esc_url(get_permalink()); ?>" />
  <meta property="og:site_name" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?>" />
  <meta property="og:locale" content="nl" />
  <meta name="DC.title" content="<?php echo the_title(); ?>">
  <meta name="DC.creator" content="Door Dennis">
  <meta name="DC.subject" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="DC.publisher" content="<?php echo get_bloginfo('name'); ?>">
  <meta name="DC.language" content="nl">
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="<?php echo get_theme_mod('customTheme-main-callout-title') ? get_theme_mod('customTheme-main-callout-title') : get_bloginfo('name'); ?> | <?php the_title(); ?>">
  <meta name="twitter:description" content="<?php echo esc_attr(get_theme_mod('customTheme-main-callout-description')); ?>">
  <meta name="twitter:image" content="<?php echo esc_url(wp_get_attachment_url(get_theme_mod('customTheme-main-callout-featured-image'))); ?>">
  <link rel="canonical" href="<?php echo esc_url(get_permalink()); ?>" />
  <link rel="icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="shortcut icon" href="<?php echo get_stylesheet_directory_uri(); ?>/favicon.ico" type="image/x-icon">
  <link rel="stylesheet" href="https://use.typekit.net/sui0kvj.css">
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "<?php echo get_bloginfo('name'); ?>",
    "url": "<?php echo esc_url(home_url()); ?>",
    "logo": "<?php echo get_stylesheet_directory_uri(); ?>/logo.png",
    "description": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-description')); ?>",
    "image": "<?php echo esc_url(get_theme_mod('customTheme-main-callout-featured-image')); ?>",
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
      "contactType": "customer service",
      "contactOption": "TollFree",
      "areaServed": "NL",
      "availableLanguage": "Dutch"
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-address')); ?>"
    },
    "sameAs": [
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok')); ?>",
      "<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram')); ?>"
    ],
    "email": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-mail')); ?>",
    "telephone": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-telephone')); ?>",
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Company Information",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-company-information')); ?>"
      },
      {
        "@type": "PropertyValue",
        "name": "Analytics ID",
        "value": "<?php echo esc_html(get_theme_mod('customTheme-main-callout-analytics')); ?>"
      }
    ]
  }
  </script>

  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>', {
      'anonymize_ip': true
    });
  </script>

  <?php wp_head(); ?>
</head>
<?php if(get_theme_mod("customTheme-main-callout-analytics")) { ?>
  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo get_theme_mod('customTheme-main-callout-analytics') ?>"></script>
  
<?php } ?>
  <body class="no-scroll">
    <header>
      <div class="background"></div>
      <div class="contentWrapper">
        <div class="col">
            <a href="/" title="Logo | <?php echo get_theme_mod('customTheme-main-callout-title') ?>" class="logo">
              <svg data-name="Group 1" xmlns="http://www.w3.org/2000/svg" width="162" height="36.995" viewBox="0 0 162 36.995">
                <path data-name="Path 1" d="M18.425,36.995A18.5,18.5,0,1,0,0,18.5,18.461,18.461,0,0,0,18.425,36.995Z" fill="#fff"/>
                <path data-name="Path 2" d="M18.425,35.313A16.816,16.816,0,1,0,1.675,18.5,16.783,16.783,0,0,0,18.425,35.313Z" fill="#ecc76f"/>
                <path idata-name="Path 3" d="M24.117,6.469l-5.692,5.715L12.732,6.469" fill="#fff"/>
                <path data-name="Path 4" d="M25.808,11.385H23.466a.447.447,0,0,1-.1.04,2.548,2.548,0,0,0-2.068,2.4c-.025.764-.02,1.527,0,2.291a1.735,1.735,0,0,0,1.641,1.7c.512.03,1.029.01,1.541.015a.474.474,0,0,0,.085-.025V13.965A.282.282,0,0,1,24.7,13.7c.328-.21.646-.434.974-.649a.266.266,0,0,0,.139-.25V11.385Z" fill="#fff"/>
                <path data-name="Path 5" d="M12.3,17.589v.24c.537,0,1.044.025,1.551,0a1.753,1.753,0,0,0,1.72-1.847c0-.679.01-1.358,0-2.036a2.455,2.455,0,0,0-1.263-2.2A5.61,5.61,0,0,0,13.4,11.4H11.057c0,.464.01.928,0,1.392a.3.3,0,0,0,.154.284c.323.2.636.429.959.634a.274.274,0,0,1,.139.27V17.6Z" fill="#fff"/>
                <path data-name="Path 6" d="M25.738,16.641v4.632a4.357,4.357,0,0,0,2.446-2.615c.109-.309.159-.639.239-.963V16.636H25.738Z" fill="#fff"/>
                <path data-name="Path 7" d="M24.555,18.992H23.009a2.85,2.85,0,0,1-2.774-2.206,9.812,9.812,0,0,1-.1-1.537c-.005-.09-.01-.175-.015-.26H16.734c0,.245.015.474,0,.7a6.228,6.228,0,0,1-.119,1.183,2.879,2.879,0,0,1-2.784,2.121H12.32c0,.065-.01.1-.01.13,0,1.787,0,3.579.005,5.366a.354.354,0,0,0,.124.235c.472.4.945.8,1.432,1.183a.561.561,0,0,1,.239.5,6.138,6.138,0,0,0,.134,1.807,4.325,4.325,0,0,0,8.437-.3c.084-.544.055-1.1.09-1.657A.378.378,0,0,1,22.894,26c.492-.424.994-.838,1.5-1.248a.412.412,0,0,0,.169-.359c0-1.722,0-3.449,0-5.171v-.24Zm-7.86,2.645a.138.138,0,0,1-.139.135l-.622-.015-.015.624a.138.138,0,0,1-.139.135l-.447-.01a.138.138,0,0,1-.134-.14l.015-.624-.621-.015a.138.138,0,0,1-.134-.14l.01-.449A.138.138,0,0,1,14.607,21l.621.015.015-.624a.138.138,0,0,1,.139-.135l.447.01a.138.138,0,0,1,.134.14l-.015.624.622.015a.138.138,0,0,1,.134.14Zm1.73,7.227c-.522-.524-1.074-1.073-1.626-1.627.253-.255.512-.514.785-.784.258.275.527.554.79.834l.845-.864c.3.309.572.574.82.824-.532.534-1.079,1.088-1.616,1.622ZM20.861,21.5a.358.358,0,1,1-.254-.439A.36.36,0,0,1,20.861,21.5Zm.045-.953a.358.358,0,1,1,.254.439A.36.36,0,0,1,20.906,20.549Zm.721,1.677a.358.358,0,1,1-.253-.439A.36.36,0,0,1,21.626,22.226Zm.741-.754a.358.358,0,1,1-.254-.439A.36.36,0,0,1,22.367,21.472Z" fill="#fff"/>
                <path data-name="Path 8" d="M9.232,19.755a4.352,4.352,0,0,0,1.894,1.512V16.656H8.452A4.471,4.471,0,0,0,9.232,19.755Z" fill="#fff"/>
                <path data-name="Path 9" d="M67.082,18.188V31.38a28.341,28.341,0,0,1-12.33,2.775,28.778,28.778,0,0,1-14.517-3.678V6.529A33.558,33.558,0,0,1,55.23,2.85,35.69,35.69,0,0,1,66.893,4.667v8.939a32.722,32.722,0,0,0-9.709-1.722,29.328,29.328,0,0,0-6.95.719V24.5a15.605,15.605,0,0,0,4.569.619,21.953,21.953,0,0,0,3.475-.24V18.193h8.8Z" fill="#fff"/>
                <path data-name="Path 10" d="M85.218,34.15A30.5,30.5,0,0,1,70.7,30.422V6.573a30.126,30.126,0,0,1,29.034,0V30.422A30.517,30.517,0,0,1,85.218,34.15ZM89.743,24.3V12.688a12.986,12.986,0,0,0-9.048,0V24.3a12.986,12.986,0,0,0,9.048,0Z" fill="#fff"/>
                <path data-name="Path 11" d="M121.775,26.454h-8.283v6.978h-9.9V6.573a28.316,28.316,0,0,1,28.089,0V33.431h-9.9V26.454Zm-8.283-8.6h8.283V12.688a10.948,10.948,0,0,0-8.283,0Z" fill="#fff"/>
                <path data-name="Path 12" d="M134.393,3.564H162v8.365h-8.8V33.436h-10V11.929h-8.8Z" fill="#fff"/>
              </svg>
            </a>
        </div>
        <div class="col">
          <div class="innerMenu">
              <?php wp_nav_menu( array(
                'menu' => 'primary-menu',
              ) ); ?>
          </div>
        </div>
        <div class="col">
          <?php $button = get_field('header_button', 'option'); ?>
          <?php if ($button): ?>
            <?php render_button_from_array($button); ?>
          <?php endif; ?>
        </div>
      </div>
    </header>
  <div class="backgroundCursor"></div>
  <div class="lines" data-init>
    <div class="contentWrapper">
      <div class="innerWrapper">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
      </div>
    </div>
  </div>
  <div id="pageContainer" class="transition-fade blocks">
   