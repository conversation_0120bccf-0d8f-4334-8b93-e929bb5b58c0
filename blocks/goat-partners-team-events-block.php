<section class="goatPartnersTeamEventsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("title")) : ?>
                <h2 class="mediumTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("button")) : ?>
                <div class="buttonWrapper">
                    <?php render_button('button'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="marqueeWrapper" data-init>
            <?php if (get_field("background_image")) : ?>
                <div class="backgroundImage">
                    <img class="lazy" data-src="<?php echo esc_url(get_field('background_image')['sizes']['medium_large']); ?>" alt="<?php echo esc_attr(get_field('background_image')['alt']); ?>">
                </div>
            <?php endif; ?>
            <? if (!get_field("hide_sponsors")) : ?>
            <div class="marquee" data-marquee data-marquee-direction="right" data-marquee-speed="25" data-marquee-scroll-speed="5" data-marquee-swipe="true">
                <div class="marqueeScroll">    
                    <div class="itemsContainer">
                        <?php 
                        $partners = get_posts(array(
                            'post_type' => 'partner',
                            'posts_per_page' => 6,
                            'post_status' => 'publish'
                        ));
                        
                        if ($partners) : 
                            foreach ($partners as $partner) : 
                                $partner_id = $partner->ID;
                        ?>
                            <div class="item partner">
                                <?php if (get_field('partner_website', $partner_id)) : ?>
                                    <a title="Visit <?php echo get_the_title($partner_id); ?>" href="<?php echo esc_url(get_field('partner_website', $partner_id)); ?>" target="_blank" class="partnerLink">
                                <?php endif; ?>
                                
                                <?php if (get_field('partner_logo', $partner_id)) : ?>
                                    <img src="<?php echo esc_url(get_field('partner_logo', $partner_id)['sizes']['medium']); ?>" alt="<?php echo esc_attr(get_the_title($partner_id)); ?>">
                                <?php else : ?>
                                    <?php echo get_the_post_thumbnail($partner_id, 'medium'); ?>
                                <?php endif; ?>
                                
                                <?php if (get_field('partner_website', $partner_id)) : ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php 
                            endforeach;
                        endif; 
                        ?>
                    </div>
                </div>
            </div>
            <? endif; ?>
        </div>
        <div class="col">
            <!-- uppercase text -->
            <h3 class="subTitle primary"><?php echo strtoupper(get_field("events_title")); ?></h3>
        </div>
        <div class="col right">
            <?php if (get_field("events_link")) : ?>
                <div class="linkToEvents">
                    <?php render_text_link('events_link'); ?>
                </div>
            <?php endif; ?>
        </div>
        <div class="events" data-show-mouse>
                <?php 
                $events = get_posts(array(
                    'post_type' => 'event',
                    'posts_per_page' => 3,
                    'post_status' => 'publish',
                    'meta_key' => 'event_date',
                    'orderby' => 'meta_value',
                    'order' => 'ASC'
                ));
                
                if ($events) : 
                    foreach ($events as $event) : 
                        $event_id = $event->ID;
                ?>
                <a href="<?php echo get_permalink($event_id); ?>" class="event">
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($event_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($event_id)); ?>">
                        </div>
                    </div>
                    <div class="eventInfo">
                        <?php if (get_field('event_date', $event_id)) : ?>
                            <div class="smallTitle primary"><?php echo get_field('event_date', $event_id); ?></div>
                        <?php endif; ?>
                        <h3 class="eventTitle normalTitle"><?php echo get_the_title($event_id); ?></h3>
                    </div>
                </a>
                <?php 
                    endforeach;
                endif; 
                ?>
            </div>
        </div>
    </div>
</section>
