<section class="goatStepsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <?php if (get_field("steps")) : ?>
            <div class="stepsContainer">
                <?php 
                $step_count = 0;
                while (have_rows('steps')) : the_row(); 
                    $step_count++;
                ?>
                    <div class="stepItem" data-step="<?php echo $step_count; ?>">
                        <div class="stepNumber">
                            <span class="number"><?php echo $step_count; ?></span>
                        </div>
                        
                        <div class="stepContent">
                            <?php if (get_sub_field('step_icon')) : ?>
                                <div class="stepIcon">
                                    <img src="<?php echo esc_url(get_sub_field('step_icon')['url']); ?>" alt="<?php echo esc_attr(get_sub_field('step_icon')['alt']); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('step_title')) : ?>
                                <h3 class="stepTitle"><?php the_sub_field('step_title'); ?></h3>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('step_description')) : ?>
                                <div class="stepDescription"><?php the_sub_field('step_description'); ?></div>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('step_image')) : ?>
                                <div class="stepImage">
                                    <img src="<?php echo esc_url(get_sub_field('step_image')['url']); ?>" alt="<?php echo esc_attr(get_sub_field('step_image')['alt']); ?>">
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_sub_field('step_button')) : ?>
                                <div class="stepButton">
                                    <?php render_text_link_sub('step_button'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($step_count < count(get_field('steps'))) : ?>
                            <div class="stepConnector">
                                <div class="connectorLine"></div>
                                <div class="connectorArrow">
                                    <i class="icon-arrow-down"></i>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php endif; ?>
        
        <?php if (get_field("bottom_cta_section")) : ?>
            <div class="bottomCTA">
                <?php if (get_field("bottom_cta_title")) : ?>
                    <h3 class="ctaTitle"><?php the_field("bottom_cta_title"); ?></h3>
                <?php endif; ?>
                
                <?php if (get_field("bottom_cta_description")) : ?>
                    <div class="ctaDescription"><?php the_field("bottom_cta_description"); ?></div>
                <?php endif; ?>
                
                <?php if (get_field("bottom_cta_button")) : ?>
                    <div class="ctaButton">
                        <?php render_button('bottom_cta_button'); ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
