<section class="goatThreeEventsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="eventsGrid">
            <?php 
            $selected_events = get_field('selected_events');
            if (!$selected_events) {
                // If no specific events selected, show latest 3
                $selected_events = get_posts(array(
                    'post_type' => 'event',
                    'posts_per_page' => 3,
                    'post_status' => 'publish',
                    'meta_key' => 'event_date',
                    'orderby' => 'meta_value',
                    'order' => 'ASC'
                ));
            }
            
            if ($selected_events) : 
                foreach ($selected_events as $event) : 
                    $event_id = $event->ID;
                    $event_images = get_field('event_images', $event_id);
                    $featured_image = $event_images ? $event_images[0] : null;
            ?>
                <div class="eventCard">
                    <a href="<?php echo get_permalink($event_id); ?>" class="eventLink">
                        <div class="eventImage">
                            <?php if ($featured_image) : ?>
                                <img src="<?php echo esc_url($featured_image['sizes']['large']); ?>" alt="<?php echo esc_attr($featured_image['alt']); ?>">
                            <?php elseif (has_post_thumbnail($event_id)) : ?>
                                <?php echo get_the_post_thumbnail($event_id, 'large'); ?>
                            <?php else : ?>
                                <div class="placeholderImage">
                                    <i class="icon-calendar"></i>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_field('event_date', $event_id)) : ?>
                                <div class="eventDateBadge">
                                    <?php 
                                    $event_date = get_field('event_date', $event_id);
                                    $date = DateTime::createFromFormat('Y-m-d', $event_date);
                                    if ($date) : ?>
                                        <span class="day"><?php echo $date->format('d'); ?></span>
                                        <span class="month"><?php echo $date->format('M'); ?></span>
                                    <?php else : ?>
                                        <span class="date"><?php echo $event_date; ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="eventInfo">
                            <h3 class="eventTitle"><?php echo get_the_title($event_id); ?></h3>
                            
                            <?php if (get_field('event_location', $event_id)) : ?>
                                <div class="eventLocation">
                                    <i class="icon-location"></i>
                                    <span><?php echo get_field('event_location', $event_id); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (get_field('event_description', $event_id)) : ?>
                                <div class="eventDescription">
                                    <p><?php echo wp_trim_words(get_field('event_description', $event_id), 20); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="eventMeta">
                                <?php if (get_field('event_date', $event_id)) : ?>
                                    <div class="eventDate">
                                        <i class="icon-calendar"></i>
                                        <span><?php echo get_field('event_date', $event_id); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="readMore">
                                    <span>Read More</span>
                                    <i class="icon-arrow-right"></i>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        
        <?php if (get_field("view_all_events_button")) : ?>
            <div class="ctaWrapper">
                <?php render_button('view_all_events_button'); ?>
            </div>
        <?php endif; ?>
    </div>
</section>
