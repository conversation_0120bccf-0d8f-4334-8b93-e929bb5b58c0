<section class="goatTeamOverviewBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <?php if (get_field("show_team_filters")) : ?>
            <div class="teamFilters">
                <button class="filterBtn active" data-filter="all">All Team</button>
                <?php if (get_field("team_categories")) : ?>
                    <?php while (have_rows('team_categories')) : the_row(); ?>
                        <button class="filterBtn" data-filter="<?php echo sanitize_title(get_sub_field('category_name')); ?>">
                            <?php the_sub_field('category_name'); ?>
                        </button>
                    <?php endwhile; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div class="teamMembersGrid">
            <?php 
            $team_members = get_posts(array(
                'post_type' => 'team_member',
                'posts_per_page' => -1,
                'post_status' => 'publish',
                'orderby' => 'menu_order',
                'order' => 'ASC'
            ));
            
            if ($team_members) : 
                foreach ($team_members as $member) : 
                    $member_id = $member->ID;
                    $show_detail_page = get_field('show_detail_page', $member_id);
                    $member_category = get_field('member_category', $member_id);
                    $category_class = $member_category ? sanitize_title($member_category) : 'general';
            ?>
                <div class="teamMemberCard" data-category="<?php echo $category_class; ?>">
                    <?php if ($show_detail_page) : ?>
                        <a href="<?php echo get_permalink($member_id); ?>" class="memberLink">
                    <?php endif; ?>
                    
                    <div class="memberImage">
                        <?php if (has_post_thumbnail($member_id)) : ?>
                            <?php echo get_the_post_thumbnail($member_id, 'medium_large'); ?>
                        <?php else : ?>
                            <div class="placeholderImage">
                                <i class="icon-user"></i>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($show_detail_page) : ?>
                            <div class="memberOverlay">
                                <div class="viewProfile">
                                    <span>View Profile</span>
                                    <i class="icon-arrow-right-up"></i>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="memberInfo">
                        <h3 class="memberName"><?php echo get_the_title($member_id); ?></h3>
                        
                        <?php if (get_field('member_role', $member_id)) : ?>
                            <div class="memberRole"><?php echo get_field('member_role', $member_id); ?></div>
                        <?php endif; ?>
                        
                        <?php if (get_field('member_bio', $member_id)) : ?>
                            <div class="memberBio"><?php echo wp_trim_words(get_field('member_bio', $member_id), 15); ?></div>
                        <?php endif; ?>
                        
                        <div class="memberSocials">
                            <?php 
                            $socials = array(
                                'twitch' => 'icon-twitch',
                                'tiktok' => 'icon-tiktok', 
                                'instagram' => 'icon-instagram',
                                'twitter' => 'icon-twitter',
                                'discord' => 'icon-discord',
                                'facebook' => 'icon-facebook',
                                'youtube' => 'icon-youtube'
                            );
                            
                            foreach ($socials as $social => $icon) :
                                if (get_field('member_' . $social, $member_id)) : ?>
                                    <a href="<?php echo esc_url(get_field('member_' . $social, $member_id)); ?>" class="socialLink" target="_blank" rel="noopener">
                                        <i class="<?php echo $icon; ?>"></i>
                                    </a>
                                <?php endif;
                            endforeach; ?>
                        </div>
                        
                        <?php if (get_field('member_achievements', $member_id)) : ?>
                            <div class="memberAchievements">
                                <?php while (have_rows('member_achievements', $member_id)) : the_row(); ?>
                                    <div class="achievement">
                                        <span class="achievementTitle"><?php the_sub_field('achievement_title'); ?></span>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($show_detail_page) : ?>
                        </a>
                    <?php endif; ?>
                </div>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        
        <?php if (get_field("join_team_section")) : ?>
            <div class="joinTeamSection">
                <div class="joinTeamContent">
                    <?php if (get_field("join_team_title")) : ?>
                        <h3 class="joinTeamTitle"><?php the_field("join_team_title"); ?></h3>
                    <?php endif; ?>
                    
                    <?php if (get_field("join_team_description")) : ?>
                        <div class="joinTeamDescription"><?php the_field("join_team_description"); ?></div>
                    <?php endif; ?>
                    
                    <?php if (get_field("join_team_button")) : ?>
                        <div class="joinTeamCTA">
                            <?php render_button('join_team_button'); ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>
