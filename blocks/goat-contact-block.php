<section class="goatContactBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="contactContent">
            <div class="contactInfo">
                <div class="sectionHeader">
                    <?php if (get_field("subtitle")) : ?>
                        <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
                    <?php endif; ?>
                    
                    <?php if (get_field("title")) : ?>
                        <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
                    <?php endif; ?>
                    
                    <?php if (get_field("description")) : ?>
                        <div class="description"><?php the_field("description"); ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="contactDetails">
                    <?php if (get_field("contact_email")) : ?>
                        <div class="contactItem">
                            <div class="contactIcon">
                                <i class="icon-mail"></i>
                            </div>
                            <div class="contactText">
                                <h4>Email</h4>
                                <a href="mailto:<?php echo esc_attr(get_field('contact_email')); ?>"><?php the_field('contact_email'); ?></a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (get_field("contact_phone")) : ?>
                        <div class="contactItem">
                            <div class="contactIcon">
                                <i class="icon-phone"></i>
                            </div>
                            <div class="contactText">
                                <h4>Phone</h4>
                                <a href="tel:<?php echo esc_attr(get_field('contact_phone')); ?>"><?php the_field('contact_phone'); ?></a>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (get_field("contact_address")) : ?>
                        <div class="contactItem">
                            <div class="contactIcon">
                                <i class="icon-location"></i>
                            </div>
                            <div class="contactText">
                                <h4>Address</h4>
                                <p><?php the_field('contact_address'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (get_field("contact_hours")) : ?>
                        <div class="contactItem">
                            <div class="contactIcon">
                                <i class="icon-clock"></i>
                            </div>
                            <div class="contactText">
                                <h4>Hours</h4>
                                <p><?php the_field('contact_hours'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (get_field("social_media_section")) : ?>
                    <div class="socialMedia">
                        <h4>Follow Us</h4>
                        <div class="socialLinks">
                            <?php 
                            $socials = array(
                                'facebook' => 'Facebook',
                                'twitter' => 'Twitter', 
                                'instagram' => 'Instagram',
                                'discord' => 'Discord',
                                'twitch' => 'Twitch',
                                'youtube' => 'YouTube',
                                'tiktok' => 'TikTok'
                            );
                            
                            foreach ($socials as $social => $label) :
                                if (get_field('social_' . $social)) : ?>
                                    <a href="<?php echo esc_url(get_field('social_' . $social)); ?>" class="socialLink" target="_blank" rel="noopener">
                                        <i class="icon-<?php echo $social; ?>"></i>
                                        <span><?php echo $label; ?></span>
                                    </a>
                                <?php endif;
                            endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="contactForm">
                <?php if (get_field("form_title")) : ?>
                    <h3 class="formTitle"><?php the_field("form_title"); ?></h3>
                <?php endif; ?>
                
                <?php if (get_field("contact_form_shortcode")) : ?>
                    <div class="formWrapper">
                        <?php echo do_shortcode(get_field('contact_form_shortcode')); ?>
                    </div>
                <?php else : ?>
                    <!-- Default contact form -->
                    <form class="defaultContactForm" method="post" action="">
                        <div class="formGroup">
                            <label for="contact_name">Name *</label>
                            <input type="text" id="contact_name" name="contact_name" required>
                        </div>
                        
                        <div class="formGroup">
                            <label for="contact_email">Email *</label>
                            <input type="email" id="contact_email" name="contact_email" required>
                        </div>
                        
                        <div class="formGroup">
                            <label for="contact_subject">Subject</label>
                            <input type="text" id="contact_subject" name="contact_subject">
                        </div>
                        
                        <div class="formGroup">
                            <label for="contact_message">Message *</label>
                            <textarea id="contact_message" name="contact_message" rows="5" required></textarea>
                        </div>
                        
                        <div class="formGroup">
                            <button type="submit" class="button submitBtn">
                                <span class="innerText">Send Message</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>
