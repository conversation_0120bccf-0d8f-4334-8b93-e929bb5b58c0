<section class="goatPartnersBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?> data-show-cursor>
    <div class="contentWrapper smallest">
        <div class="sectionHeader">
            <?php if (get_field("title")) : ?>
                <h2 class="subTitle primary"><?php the_field("title"); ?></h2>
            <?php endif; ?>
        </div>
        
        <div class="partners">
            <?php 
            $selected_partners = get_posts(array(
                'post_type' => 'partner',
                'posts_per_page' => -1,
                'post_status' => 'publish'
            ));
            if ($selected_partners) : 
                foreach ($selected_partners as $partner) : 
                    $partner_id = $partner->ID;
            ?>
                <a href="<?php echo esc_url(get_field('partner_website', $partner_id)); ?>" target="_blank" title="Visit <?php echo get_the_title($partner_id); ?>" class="partner">
                    
                    <div class="partnerLogo">
                        <?php if (get_field('partner_logo', $partner_id)) : ?>
                            <img src="<?php echo esc_url(get_field('partner_logo', $partner_id)['url']); ?>" alt="<?php echo esc_attr(get_the_title($partner_id)); ?>">
                        <?php else : ?>
                            <?php echo get_the_post_thumbnail($partner_id, 'medium'); ?>
                        <?php endif; ?>
                    </div>
                    
                    <div class="partnerInfo">
                        <h3 class="subTitle"><?php echo get_the_title($partner_id); ?></h3>
                        <div class="text">
                            <?php if (get_field('partner_description', $partner_id)) : ?>
                                <div class="partnerDescription">
                                    <p><?php echo wp_trim_words(get_field('partner_description', $partner_id), 20); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </a>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
    </div>
</section>
