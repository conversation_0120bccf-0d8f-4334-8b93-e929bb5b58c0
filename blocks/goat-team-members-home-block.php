<section class="goatTeamMembersHomeBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?> data-show-cursor>
    <div class="contentWrapper">
        <?php if (get_field("title")) : ?>
            <h2 class="mediumTitle">
                <?php
                $title = get_field("title");
                $words = explode(' ', $title);
                if (count($words) >= 2) {
                    $last_word = array_pop($words);
                    echo implode(' ', $words) . ' <span class="highlight">' . $last_word . '</span>';
                } else {
                    echo $title;
                }
                ?>
            </h2>
        <?php endif; ?>
        <div class="teamMembersGrid">
            <?php
            $featured_members = get_field('featured_members');
            if (!$featured_members) {
                // If no specific members selected, show latest 3
                $featured_members = get_posts(array(
                    'post_type' => 'team-member',
                    'posts_per_page' => 3,
                    'post_status' => 'publish'
                ));
            }
            if ($featured_members) :
                $count = 0;
                foreach ($featured_members as $member) :
                    if ($count >= 3) break; // Limit to 3 members
                    $member_id = $member->ID;
                    $show_detail_page = get_field('show_detail_page', $member_id);
                ?>
                <article class="teamMemberCard">
                    <?php if ($show_detail_page) : ?>
                        <a href="<?php echo get_permalink($member_id); ?>" class="memberLink">
                    <?php endif; ?>

                    <div class="memberImage">
                        <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($member_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($member_id)); ?>">
                    </div>

                    <div class="memberInfo">
                        <h3 class="memberName"><?php echo get_the_title($member_id); ?></h3>
                        <div class="memberSocials">
                            <?php
                            $socials = array('twitch', 'tiktok', 'instagram', 'twitter');
                            foreach ($socials as $social) :
                                if (get_field('member_' . $social, $member_id)) : ?>
                                    <a href="<?php echo esc_url(get_field('member_' . $social, $member_id)); ?>" class="socialLink" target="_blank">
                                        <i class="icon-<?php echo $social; ?>"></i>
                                    </a>
                                <?php endif;
                            endforeach; ?>
                        </div>
                    </div>

                    <?php if ($show_detail_page) : ?>
                        </a>
                    <?php endif; ?>
                </article>
            <?php
                $count++;
                endforeach;
            endif;
            ?>
            <article class="descriptionCard">
                <?php if (get_field("description")) : ?>
                    <div class="text"><p><?php the_field("description"); ?></p></div>
                <?php endif; ?>

                <?php if (get_field("view_all_button")) : ?>
                    <div class="ctaWrapper">
                        <?php render_button('view_all_button'); ?>
                    </div>
                <?php endif; ?>
            </article>
        </div>
    </div>
</section>
