<section class="goatTextTwoColumnBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <?php if (get_field("section_header")) : ?>
            <div class="sectionHeader">
                <?php if (get_field("subtitle")) : ?>
                    <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
                <?php endif; ?>
                
                <?php if (get_field("title")) : ?>
                    <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
                <?php endif; ?>
                
                <?php if (get_field("description")) : ?>
                    <div class="description"><?php the_field("description"); ?></div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div class="twoColumnContent">
            <div class="leftColumn">
                <?php if (get_field("left_column_title")) : ?>
                    <h3 class="columnTitle"><?php the_field("left_column_title"); ?></h3>
                <?php endif; ?>
                
                <?php if (get_field("left_column_content")) : ?>
                    <div class="columnContent"><?php the_field("left_column_content"); ?></div>
                <?php endif; ?>
                
                <?php if (get_field("left_column_image")) : ?>
                    <div class="columnImage">
                        <img src="<?php echo esc_url(get_field('left_column_image')['url']); ?>" alt="<?php echo esc_attr(get_field('left_column_image')['alt']); ?>">
                    </div>
                <?php endif; ?>
                
                <?php if (get_field("left_column_button")) : ?>
                    <div class="columnButton">
                        <?php render_button('left_column_button'); ?>
                    </div>
                <?php endif; ?>
            </div>
            
            <div class="rightColumn">
                <?php if (get_field("right_column_title")) : ?>
                    <h3 class="columnTitle"><?php the_field("right_column_title"); ?></h3>
                <?php endif; ?>
                
                <?php if (get_field("right_column_content")) : ?>
                    <div class="columnContent"><?php the_field("right_column_content"); ?></div>
                <?php endif; ?>
                
                <?php if (get_field("right_column_image")) : ?>
                    <div class="columnImage">
                        <img src="<?php echo esc_url(get_field('right_column_image')['url']); ?>" alt="<?php echo esc_attr(get_field('right_column_image')['alt']); ?>">
                    </div>
                <?php endif; ?>
                
                <?php if (get_field("right_column_button")) : ?>
                    <div class="columnButton">
                        <?php render_button('right_column_button'); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (get_field("bottom_cta_section")) : ?>
            <div class="bottomCTA">
                <?php if (get_field("bottom_cta_title")) : ?>
                    <h3 class="ctaTitle"><?php the_field("bottom_cta_title"); ?></h3>
                <?php endif; ?>
                
                <?php if (get_field("bottom_cta_description")) : ?>
                    <div class="ctaDescription"><?php the_field("bottom_cta_description"); ?></div>
                <?php endif; ?>
                
                <?php if (get_field("bottom_cta_button")) : ?>
                    <div class="ctaButton">
                        <?php render_button('bottom_cta_button'); ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
