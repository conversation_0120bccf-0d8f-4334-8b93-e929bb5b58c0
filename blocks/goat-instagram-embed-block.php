<section class="goatInstagramEmbedBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            
            <?php if (get_field("title")) : ?>
                <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
            
            <?php if (get_field("description")) : ?>
                <div class="description"><?php the_field("description"); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="instagramContent">
            <?php if (get_field("instagram_feed_type") === 'hashtag' && get_field("instagram_hashtag")) : ?>
                <!-- Instagram Hashtag Feed -->
                <div class="instagramHashtagFeed" data-hashtag="<?php echo esc_attr(get_field('instagram_hashtag')); ?>">
                    <div class="instagramGrid">
                        <!-- Instagram posts will be loaded here via JavaScript -->
                        <div class="instagramPlaceholder">
                            <p>Loading Instagram posts...</p>
                        </div>
                    </div>
                </div>
                
            <?php elseif (get_field("instagram_feed_type") === 'manual' && get_field("instagram_posts")) : ?>
                <!-- Manual Instagram Posts -->
                <div class="instagramManualFeed">
                    <div class="instagramGrid">
                        <?php while (have_rows('instagram_posts')) : the_row(); ?>
                            <div class="instagramPost">
                                <?php if (get_sub_field('post_url')) : ?>
                                    <a href="<?php echo esc_url(get_sub_field('post_url')); ?>" target="_blank" class="postLink">
                                <?php endif; ?>
                                
                                <?php if (get_sub_field('post_image')) : ?>
                                    <div class="postImage">
                                        <img src="<?php echo esc_url(get_sub_field('post_image')['url']); ?>" alt="<?php echo esc_attr(get_sub_field('post_image')['alt']); ?>">
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (get_sub_field('post_caption')) : ?>
                                    <div class="postCaption">
                                        <p><?php echo wp_trim_words(get_sub_field('post_caption'), 15); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="instagramIcon">
                                    <i class="icon-instagram"></i>
                                </div>
                                
                                <?php if (get_sub_field('post_url')) : ?>
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php endwhile; ?>
                    </div>
                </div>
                
            <?php elseif (get_field("instagram_embed_code")) : ?>
                <!-- Instagram Embed Code -->
                <div class="instagramEmbed">
                    <?php echo get_field('instagram_embed_code'); ?>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if (get_field("instagram_profile_link")) : ?>
            <div class="instagramCTA">
                <?php 
                $link = get_field('instagram_profile_link');
                if ($link) : ?>
                    <a href="<?php echo esc_url($link['url']); ?>" class="button instagramButton" target="<?php echo esc_attr($link['target'] ?: '_blank'); ?>">
                        <span class="innerText">
                            <i class="icon-instagram"></i>
                            <?php echo esc_html($link['title']); ?>
                        </span>
                        <span class="arrows">
                            <i class="icon-arrow-right-up"></i>
                            <i class="icon-arrow-right-up"></i>
                        </span>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
