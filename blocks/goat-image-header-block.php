<section class="goatImageHeaderBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="backgroundImage">
            <?php if (get_field("background_image")) : ?>
                <img class="lazy" data-src="<?php echo esc_url(get_field('background_image')['url']); ?>" alt="<?php echo esc_attr(get_field('background_image')['alt']); ?>">
            <?php endif; ?>
        </div>
        <div class="sectionHeader">
            <?php if (get_field("subtitle")) : ?>
                <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
            <?php endif; ?>
            <?php if (get_field("title")) : ?>
                <h1 class="mediumTitle"><?php the_field("title"); ?></h1>
            <?php endif; ?>
            <?php if (get_field("button")) : ?>
                <div class="buttonWrapper">
                    <?php render_button('button'); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="contentWrapper smaller">
        <div class="cols">
            <div class="col">
                <div class="text">
                    <?php echo(get_field("text_left")); ?>
                </div>
            </div>
            <div class="col small">
                <div class="text">
                    <?php echo(get_field("text_right")); ?>
                </div>
            </div>
        </div>
    </div>
</section>
