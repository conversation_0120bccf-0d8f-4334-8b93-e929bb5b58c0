.goatThreeEventsBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .eventsGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
            
            @media (max-width: 768px) {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .eventCard {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }
                
                .eventImage {
                    position: relative;
                    height: 200px;
                    overflow: hidden;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }
                    
                    .placeholderImage {
                        width: 100%;
                        height: 100%;
                        background: #f8f9fa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #666;
                        font-size: 2rem;
                    }
                    
                    .eventDateBadge {
                        position: absolute;
                        top: 15px;
                        left: 15px;
                        background: rgba(255, 107, 53, 0.9);
                        color: white;
                        padding: 8px 12px;
                        border-radius: 8px;
                        text-align: center;
                        font-weight: bold;
                        backdrop-filter: blur(10px);
                        transition: transform 0.3s ease;
                        
                        .day {
                            display: block;
                            font-size: 1.2rem;
                            line-height: 1;
                        }
                        
                        .month {
                            display: block;
                            font-size: 0.8rem;
                            text-transform: uppercase;
                            opacity: 0.9;
                        }
                        
                        .date {
                            font-size: 0.9rem;
                        }
                        
                        &.upcoming {
                            animation: pulse 2s infinite;
                        }
                    }
                }
                
                .eventInfo {
                    padding: 25px;
                    
                    .eventTitle {
                        font-size: 1.3rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                        line-height: 1.3;
                    }
                    
                    .eventLocation {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        color: #666;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        
                        i {
                            color: #ff6b35;
                        }
                    }
                    
                    .eventDescription {
                        color: #666;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        font-size: 0.95rem;
                    }
                    
                    .eventMeta {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        
                        .eventDate {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                            color: #666;
                            font-size: 0.9rem;
                            
                            i {
                                color: #ff6b35;
                            }
                        }
                        
                        .readMore {
                            display: flex;
                            align-items: center;
                            gap: 5px;
                            color: #ff6b35;
                            font-weight: 600;
                            font-size: 0.9rem;
                            transition: transform 0.3s ease;
                            
                            i {
                                font-size: 12px;
                            }
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInUp 0.6s ease forwards;
                }
            }
        }
        
        .ctaWrapper {
            text-align: center;
            
            .button {
                background: linear-gradient(45deg, #ff6b35, #f7931e);
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 10px;
                font-weight: 600;
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                }
                
                .arrows {
                    transition: transform 0.3s ease;
                }
                
                &:hover .arrows {
                    transform: translateX(5px);
                }
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}
