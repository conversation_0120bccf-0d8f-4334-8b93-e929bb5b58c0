// out: false;
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';
.goatHomeIntroBlock {
    .contentWrapper {
        .introContent {
            position: relative;
            transform-style: preserve-3d;
            .sideImage {
                position: absolute;
                top: 50%;
                transform-style: preserve-3d;
                left: 25%;
                width: @vw100 * 5.2;
                perspective: 200px;
                height: auto;
                .transform(translateY(-50%));
                transform-style: preserve-3d;
                // mask-image: linear-gradient(to right, rgba(0,0,0,1) 0%, rgba(0,0,0,0) 100%);
                .innerImage {
                    width: 100%;
                    height: 0;
                    .paddingRatio(1,1);
                    position: relative;
                    transform-style: preserve-3d;
                }
                img {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    object-fit: contain;
                    object-position: center;
                }
            }
        }
        .introTextWrapper {
            margin-left: auto;
            position: relative;
            width: calc(50% ~"-" @vw16);
            .mediumTitle {
                position: relative;
                opacity: 0.2;
                &.overlayText {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    opacity: 1;
                    .line {
                        white-space: nowrap;
                    }
                }
            }
            .signatureTitle {
                margin: @vw5 0;
                display: block;
                width: 100%;
                text-align: right;
            }
        }
    }
}
