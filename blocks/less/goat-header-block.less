// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.goatHeaderBlock {
    &.inview {
        .sectionHeader {
            opacity: 1;
            .transitionMore(opacity, 0.3s, 0.03s, ease-in-out);
        }
        .backgroundImage {
            opacity: .5;
            .transform(translate(-50%, -50%) scale(1));
            transition: opacity 0.3s 0.03s ease-in-out, transform 0.6s 0.03s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .col {
            opacity: 1;
            .transform(translateY(0));
            transition: opacity 0.3s 0.03s ease-in-out, transform 0.6s 0.03s cubic-bezier(0, 0.55, 0.45, 1);
            .stagger(2, 0.15s, .3s);
        }
    }
    .backgroundImage {
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%, -50%) scale(.5));
        width: calc(50% ~"-" @vw64);
        height: auto;
        opacity: 0;
        overflow: hidden;
        img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }
    .sectionHeader {
        opacity: 0;
        margin: auto;
        margin-bottom: @vw100 * 2;
        display: block;
        text-align: center;
        .button {
            margin-top: @vw40;
        }
    }
    .col {
        display: inline-block;
        vertical-align: top;
        padding-right: @vw100;
        width: 60%;
        opacity: 0;
        .transform(translateY(@vw30));
        &.small {
            width: 40%;
        }
    }
}