// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.homeHeaderBlock {
  padding: @vw100 + @vw30 0 !important;
  text-align: center;
  &.inview {
    .titleWrapper {
      .buttonWrapper {
        .transform(translateY(0) skewX(0));
        opacity: 1;
      }
    }
  }
  .projectSlider {
    text-align: left;
    display: block;
    margin: auto;
    height: auto;
    width: (@vw112 * 4) + (@vw16 * 3);
    .imageSlider {
      position: relative;
      display: block;
      margin: auto;
      margin-bottom: @vw24;
      width: 100%;
      height: 0;
      .paddingRatio(1280,1600);
      .slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        clip-path: inset(100%);
        .transitionMore(clip-path, 0s, 0.9s, ease-in-out);
        z-index: 1;
        &:after {
          content: '';
          left: 0;
          top: 0;
          background: @hardBlack;
          opacity: .4;
          width: 100%;
          height: 100%;
          position: absolute;
        }
        &.active { 
          clip-path: inset(0);
          z-index: 2;
          .transitionMore(clip-path, 0.9s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
          img {
            .transform(scale(1));
            .transitionMore(transform, 0.9s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
          }
        }
        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
          .transitionMore(transform, 0.6s, .15s, cubic-bezier(0.85, 0, 0.15, 1));
          .transform(scale(1.2));
        }
      }
    }
    .titleSlider {
      color: @almostWhite;
      position: relative;
      width: 100%;
      text-transform: uppercase;
      display: block;
      .inlineCol, .divider {
        display: inline-block;
        width: auto;
      }
      .textLink {
        display: table;
      }
      .titleSlide {
        position: absolute;
        top: 0;
        left: 0;
        -webkit-filter: blur(@vw10);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s 0.03s ease-in-out, -webkit-filter 0.3s 0s ease-in-out;
        &.active {
          -webkit-filter: blur(0);
          opacity: 1;
          pointer-events: all;
          transition: opacity 0.3s 0.33s ease-in-out, -webkit-filter 0.6s 0.3s ease-in-out;
        }
      }
    }
    .innerCol {
      display: inline-block; 
      width: 50%;
      vertical-align: top;
      &:first-child {
        padding-left: @vw8;
      }
      &:last-child {
        padding-left: @vw8;
      }
      .arrowButton {
        &:not(:last-child) {
          margin-right: @vw16;
        }
      }
    }
  }
  .backgroundWrapper { 
    width: 80vw;
    position: absolute;
    pointer-events: none; 
    z-index: -1;
    height: 80vw; 
    .transform(translateY(-50%));
    top: 50%;
    left: @vw100;
    .background {
      opacity: .4;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      .rounded(50%);
      height: 100%; 
      background: @primaryColor;
      -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
      mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    }
  }
  .titleWrapper {
    position: absolute;
    .transform(translate(-50%,-50%));
    top: 60%;
    left: 50%;
    width: 100%;
    z-index: 3;
    .buttonWrapper {
      display: inline-block;
      vertical-align: middle;
      width: (@vw112 * 3) + (@vw16 * 2);
      margin-right: @vw16;
      .transform(translateY(@vw20) skewX(10deg));
      opacity: 0;
      transition: opacity 0.45s 0.9s ease-in-out, transform 0.45s .9s ease-in-out;
      .button {
        width: 100%;
      }
    }
    .subTitleWrapper {
      padding-right: (@vw112 * 2) + @vw16;
      display: inline-block;
      text-align: left;
      vertical-align: middle;
      width: calc(100% ~"-" @vw112 ~"-" @vw112 ~"-" @vw112 ~"-" @vw16 ~"-" @vw16 ~"-" @vw16);
    }
  }
  .imageWrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    -webkit-filter: blur(0px);
    transition: opacity 0.3s 0.03s ease-in-out, -webkit-filter 0.6s 0s ease-in-out;
    &.invisible {
      opacity: 0;
      -webkit-filter: blur(@vw40);
    }
    canvas {
      position: absolute;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      object-fit: contain !important;
      object-position: center;
    }
    img {
      display: none;
    }
  }
  .buttons {
    margin-top: @vw50;
    display: block;
    text-align: left;
    .button {
      &:not(:last-child) {
        margin-right: @vw20;
      }
    }
  }
}

@keyframes glowAnimation {
  0% {
    transform: translate(0, 0) scale(1);
  }
  25% {
    transform: translate(2%, -2%) scale(1.02);
  }
  50% {
    transform: translate(-2%, 2%) scale(1.03);
  }
  75% {
    transform: translate(1%, -1%) scale(1.01);
  }
  100% {
    transform: translate(-1%, 1%) scale(1);
  }
}

@media all and (max-width: 1080px) {
  .homeHeaderBlock {
    padding-top: @vw100-1080 + @vw60-1080 !important;
    padding-bottom: 0 !important;
    .projectSlider {
      width: (@vw112-1080 * 4) + (@vw16-1080 * 3);
      .imageSlider {
        margin-bottom: @vw24-1080;
        height: (@vw100-1080 * 5) + @vw30-1080;
      }
      .innerCol {
        .arrowButton {
          &:not(:last-child) {
            margin-right: @vw16-1080;
          }
        }
      }
    }
    .backgroundWrapper {
      left: @vw100-1080;
    }
    .titleWrapper {
      .buttonWrapper {
        width: (@vw112-1080 * 3) + (@vw16-1080 * 2);
        margin-right: @vw16-1080;
        .transform(translateY(@vw20-1080) skewX(10deg));
      }
      .subTitleWrapper {
        padding-right: (@vw112-1080 * 2) + @vw16-1080;
        width: calc(100% ~"-" @vw112-1080 ~"-" @vw112-1080 ~"-" @vw112-1080 ~"-" @vw16-1080 ~"-" @vw16-1080 ~"-" @vw16-1080);
      }
    }
    .buttons {
      margin-top: @vw50-1080;
      .button:not(:last-child) {
        margin-right: @vw20-1080;
      }
    }
    .imageWrapper {
      &.invisible {
        -webkit-filter: blur(@vw50-1080);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .homeHeaderBlock {
    padding-top: @vw100-580 + @vw40-580 !important;
    .projectSlider {
      width: calc(100% ~"-" @vw44-580);
      .imageSlider {
        margin-bottom: @vw24-580;
        height: (@vw100-580 * 8) + @vw30-580;
        .slide {
          &:after {
            opacity: .6;
          }
        }
      }
      .innerCol {
        .arrowButton {
          &:not(:last-child) {
            margin-right: @vw16-580;
          }
        }
      }
    }
    .backgroundWrapper {
      left: @vw100-580;
    }
    p {
      font-size: @vw16-580;
    }
    .titleWrapper {
      top: 65%;
      .buttonWrapper {
        width: 80%;
        margin-right: @vw16-580;
        .transform(translateY(@vw20-580) skewX(10deg));
      }
      .subTitleWrapper {
        margin-top: @vw22-580;
        padding-right: 0;
        width: 80%;
        text-align: center !important;
      }
    }
    .buttons {
      margin-top: @vw50-580;
      .button:not(:last-child) {
        margin-right: @vw20-580;
      }
    }
    .imageWrapper {
      &.invisible {
        -webkit-filter: blur(@vw50-580);
      }
    }
  }
}
