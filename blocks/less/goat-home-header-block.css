.goatHomeHeaderBlock {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding-top: 0 !important;
  z-index: 2;
}
.goatHomeHeaderBlock.inview .svgBackground {
  opacity: 0.4;
  -webkit-transition: opacity 0.6s 0.9s ease-in-out;
  -moz-transition: opacity 0.6s 0.9s ease-in-out;
  -o-transition: opacity 0.6s 0.9s ease-in-out;
  transition: opacity 0.6s 0.9s ease-in-out;
}
.goatHomeHeaderBlock .floatingImage {
  position: absolute;
  width: 9.259vw;
  perspective: 28.935vw;
  height: auto;
  display: block;
  right: 17.361vw;
  top: 50%;
  z-index: 2;
}
.goatHomeHeaderBlock .floatingImage img {
  width: 100%;
  height: auto;
  object-fit: contain;
  transform-style: preserve-3d;
  transform: translateZ(50px);
  -webkit-filter: drop-shadow(0 0 1.736vw rgba(236, 191, 106, 0.5));
  filter: drop-shadow(0 0 1.736vw rgba(236, 191, 106, 0.5));
  animation: dropdown 3s infinite linear;
}
.goatHomeHeaderBlock .headerBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.goatHomeHeaderBlock .headerBackground.sticky {
  position: fixed;
}
.goatHomeHeaderBlock .headerBackground .bgImage,
.goatHomeHeaderBlock .headerBackground .bgVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.goatHomeHeaderBlock .headerBackground::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}
.goatHomeHeaderBlock .svgBackground {
  position: absolute;
  opacity: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}
.goatHomeHeaderBlock .svgBackground svg {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}
.goatHomeHeaderBlock .svgBackground svg.animate path {
  animation-name: shimmer;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  transform-origin: center;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(50) {
  animation-delay: 5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(49) {
  animation-delay: 4.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(48) {
  animation-delay: 4.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(47) {
  animation-delay: 4.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(46) {
  animation-delay: 4.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(45) {
  animation-delay: 4.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(44) {
  animation-delay: 4.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(43) {
  animation-delay: 4.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(42) {
  animation-delay: 4.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(41) {
  animation-delay: 4.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(40) {
  animation-delay: 4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(39) {
  animation-delay: 3.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(38) {
  animation-delay: 3.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(37) {
  animation-delay: 3.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(36) {
  animation-delay: 3.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(35) {
  animation-delay: 3.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(34) {
  animation-delay: 3.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(33) {
  animation-delay: 3.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(32) {
  animation-delay: 3.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(31) {
  animation-delay: 3.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(30) {
  animation-delay: 3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(29) {
  animation-delay: 2.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(28) {
  animation-delay: 2.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(27) {
  animation-delay: 2.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(26) {
  animation-delay: 2.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(25) {
  animation-delay: 2.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(24) {
  animation-delay: 2.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(23) {
  animation-delay: 2.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(22) {
  animation-delay: 2.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(21) {
  animation-delay: 2.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(20) {
  animation-delay: 2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(19) {
  animation-delay: 1.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(18) {
  animation-delay: 1.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(17) {
  animation-delay: 1.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(16) {
  animation-delay: 1.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(15) {
  animation-delay: 1.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(14) {
  animation-delay: 1.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(13) {
  animation-delay: 1.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(12) {
  animation-delay: 1.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(11) {
  animation-delay: 1.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(10) {
  animation-delay: 1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(9) {
  animation-delay: 0.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(8) {
  animation-delay: 0.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(7) {
  animation-delay: 0.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(6) {
  animation-delay: 0.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(5) {
  animation-delay: 0.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(4) {
  animation-delay: 0.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(3) {
  animation-delay: 0.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(2) {
  animation-delay: 0.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(1) {
  animation-delay: 0.1s;
}
.goatHomeHeaderBlock .contentWrapper {
  position: absolute;
  z-index: 3;
  text-align: left;
  bottom: 1.157vw;
  color: white;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper {
  margin-bottom: 2rem;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper .mainLogo {
  max-width: 200px;
  height: auto;
}
@media (max-width: 768px) {
  .goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper .mainLogo {
    max-width: 150px;
  }
}
.goatHomeHeaderBlock .contentWrapper .headerContent .subTitle {
  margin-bottom: 1.157vw;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper {
  margin-bottom: 3rem;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  opacity: 0.7;
  animation: bounce 2s infinite;
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator .scrollText {
  font-size: 0.9rem;
  margin-bottom: 10px;
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator .scrollArrow {
  font-size: 1.2rem;
}
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}
@keyframes shimmer {
  0%,
  100% {
    opacity: 0.5;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  }
}
@keyframes threedimensionalAnimation {
  0%,
  100% {
    transform: translateZ(50px) scale(1);
  }
  50% {
    transform: translateZ(-50px) scale(1.1);
  }
}
@keyframes dropdown {
  0%,
  100% {
    -webkit-filter: drop-shadow(0 0 1.736vw rgba(236, 191, 106, 0.5));
    filter: drop-shadow(0 0 1.736vw rgba(236, 191, 106, 0.5));
  }
  50% {
    -webkit-filter: drop-shadow(0 0 0.579vw rgba(236, 191, 106, 0.7));
    filter: drop-shadow(0 0 0.579vw rgba(236, 191, 106, 0.7));
  }
}
