.goatHomeIntroBlock .contentWrapper .introContent {
  position: relative;
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage {
  position: absolute;
  top: 50%;
  left: 25%;
  width: 30.0924vw;
  perspective: 200px;
  height: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage .innerImage {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: contain;
  object-position: center;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper {
  margin-left: auto;
  position: relative;
  width: calc(50% - 0.926vw);
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle {
  position: relative;
  opacity: 0.2;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle.overlayText {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle.overlayText .line {
  white-space: nowrap;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .signatureTitle {
  margin: 0.289vw 0;
  display: block;
  width: 100%;
  text-align: right;
}
