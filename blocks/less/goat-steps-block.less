.goatStepsBlock {
    padding: 80px 0;
    background: #f8f9fa;
    
    .contentWrapper {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .stepsContainer {
            margin-bottom: 60px;
            
            .stepItem {
                display: grid;
                grid-template-columns: auto 1fr auto;
                gap: 30px;
                align-items: flex-start;
                margin-bottom: 40px;
                transition: all 0.3s ease;
                
                @media (max-width: 768px) {
                    grid-template-columns: auto 1fr;
                    gap: 20px;
                }
                
                &:last-child {
                    margin-bottom: 0;
                    
                    .stepConnector {
                        display: none;
                    }
                }
                
                .stepNumber {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: white;
                    border: 3px solid #e9ecef;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #666;
                    transition: all 0.3s ease;
                    flex-shrink: 0;
                    
                    @media (max-width: 768px) {
                        width: 50px;
                        height: 50px;
                        font-size: 1.2rem;
                    }
                }
                
                .stepContent {
                    background: white;
                    padding: 30px;
                    border-radius: 15px;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    
                    @media (max-width: 768px) {
                        padding: 20px;
                    }
                    
                    .stepIcon {
                        margin-bottom: 20px;
                        
                        img {
                            width: 50px;
                            height: 50px;
                            object-fit: contain;
                            transition: transform 0.3s ease;
                        }
                    }
                    
                    .stepTitle {
                        font-size: 1.3rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 15px;
                    }
                    
                    .stepDescription {
                        color: #666;
                        line-height: 1.6;
                        margin-bottom: 20px;
                    }
                    
                    .stepImage {
                        margin-bottom: 20px;
                        border-radius: 10px;
                        overflow: hidden;
                        
                        img {
                            width: 100%;
                            height: auto;
                            transition: transform 0.3s ease;
                        }
                    }
                    
                    .stepButton {
                        .textLink {
                            color: #ff6b35;
                            text-decoration: none;
                            display: inline-flex;
                            align-items: center;
                            gap: 8px;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            
                            &:hover {
                                color: #f7931e;
                                transform: translateX(3px);
                            }
                            
                            .arrows {
                                transition: transform 0.3s ease;
                            }
                            
                            &:hover .arrows {
                                transform: translateX(3px);
                            }
                        }
                    }
                }
                
                .stepConnector {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    height: 100px;
                    margin-top: 30px;
                    
                    @media (max-width: 768px) {
                        display: none;
                    }
                    
                    .connectorLine {
                        width: 2px;
                        height: 60px;
                        background: #e9ecef;
                        transition: background 0.3s ease;
                    }
                    
                    .connectorArrow {
                        color: #e9ecef;
                        font-size: 1.2rem;
                        margin-top: 10px;
                        transition: color 0.3s ease;
                    }
                    
                    &.animate-connector {
                        .connectorLine {
                            background: linear-gradient(to bottom, #ff6b35, #f7931e);
                        }
                        
                        .connectorArrow {
                            color: #ff6b35;
                        }
                    }
                }
                
                &:hover {
                    .stepNumber {
                        background: linear-gradient(45deg, #ff6b35, #f7931e);
                        border-color: #ff6b35;
                        color: white;
                        transform: scale(1.1);
                    }
                    
                    .stepContent {
                        transform: translateY(-5px);
                        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                    }
                }
                
                &.active {
                    .stepNumber {
                        background: linear-gradient(45deg, #ff6b35, #f7931e);
                        border-color: #ff6b35;
                        color: white;
                    }
                    
                    .stepContent {
                        border-left: 4px solid #ff6b35;
                    }
                }
                
                &.animate-in {
                    animation: fadeInUp 0.6s ease forwards;
                }
            }
        }
        
        .bottomCTA {
            text-align: center;
            background: white;
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            
            @media (max-width: 768px) {
                padding: 30px 20px;
            }
            
            .ctaTitle {
                font-size: 2rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 1.5rem;
                }
            }
            
            .ctaDescription {
                font-size: 1.1rem;
                color: #666;
                margin-bottom: 30px;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
                line-height: 1.6;
            }
            
            .ctaButton {
                .button {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    color: white;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
            
            &.animate-in {
                animation: fadeInUp 0.6s ease forwards;
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
