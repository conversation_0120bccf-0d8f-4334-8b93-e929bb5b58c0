.goatTeamMembersHomeBlock {
  padding: 100px 0;
  background: #1a1a1a;
  color: white;
}
.goatTeamMembersHomeBlock .contentWrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent {
  display: flex;
  align-items: flex-start;
  gap: 80px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent {
    flex-direction: column;
    gap: 60px;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent {
  flex: 2;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader {
  margin-bottom: 60px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.1;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle .highlight {
  color: #D4AF37;
  font-style: italic;
}
@media (max-width: 768px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle {
    font-size: 2.5rem;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}
@media (max-width: 768px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberLink {
  text-decoration: none;
  color: inherit;
  display: block;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage {
  position: relative;
  overflow: hidden;
  height: 320px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage:hover img {
  transform: scale(1.05);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo {
  padding: 25px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberName {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials {
  display: flex;
  gap: 12px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink:hover {
  background: #D4AF37;
  color: #1a1a1a;
  transform: translateY(-3px);
  border-color: #D4AF37;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink i {
  font-size: 18px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent {
  flex: 1;
  padding-top: 120px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent {
    padding-top: 0;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 40px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button {
  background: transparent;
  color: white;
  padding: 15px 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 15px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button:hover {
  background: #D4AF37;
  border-color: #D4AF37;
  color: #1a1a1a;
  transform: translateY(-2px);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
  font-size: 1.2rem;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
