// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less'; 
 
.smallHeaderBlock {
  text-align: center;
  .text {
    display: block;
    margin: auto;
    margin-top: @vw80;
    width: (@vw112 * 4) + (@vw16 * 3);
  }
}

@media all and (max-width: 1080px) {
  .smallHeaderBlock {
    .text {
      margin-top: @vw80-1080;
      width: (@vw112-1080 * 4) + (@vw16-1080 * 3);
    }
  }
}

@media all and (max-width: 580px) {
  .smallHeaderBlock {
    .bigTitle {
      font-size: @vw50-580;
    }
    .text {
      margin-top: @vw80-580;
      width: (@vw112-580 * 4) + (@vw16-580 * 3);
    }
  }
}
