.goatInstagramEmbedBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .instagramContent {
            margin-bottom: 50px;
            
            .instagramGrid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                
                .instagramPost {
                    position: relative;
                    background: white;
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                    }
                    
                    .postImage {
                        aspect-ratio: 1;
                        overflow: hidden;
                        
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transition: transform 0.3s ease;
                        }
                    }
                    
                    .postCaption {
                        padding: 15px;
                        
                        p {
                            color: #333;
                            font-size: 0.9rem;
                            line-height: 1.4;
                            margin: 0;
                        }
                    }
                    
                    .instagramIcon {
                        position: absolute;
                        top: 15px;
                        right: 15px;
                        width: 30px;
                        height: 30px;
                        background: rgba(0, 0, 0, 0.7);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        opacity: 0.8;
                        transition: all 0.3s ease;
                        
                        i {
                            font-size: 14px;
                        }
                    }
                    
                    &:hover {
                        .postImage img {
                            transform: scale(1.05);
                        }
                        
                        .instagramIcon {
                            opacity: 1;
                            transform: scale(1.1);
                        }
                    }
                    
                    &.animate-in {
                        animation: fadeInUp 0.6s ease forwards;
                    }
                }
            }
            
            .instagramPlaceholder {
                text-align: center;
                padding: 60px 20px;
                background: #f8f9fa;
                border-radius: 15px;
                
                .instagram-loading {
                    .loading-spinner {
                        width: 40px;
                        height: 40px;
                        border: 4px solid #e9ecef;
                        border-top: 4px solid #ff6b35;
                        border-radius: 50%;
                        animation: spin 1s linear infinite;
                        margin: 20px auto;
                    }
                }
                
                p {
                    color: #666;
                    font-size: 1.1rem;
                    margin-bottom: 10px;
                    
                    small {
                        font-size: 0.9rem;
                        opacity: 0.7;
                    }
                }
            }
        }
        
        .instagramCTA {
            text-align: center;
            
            .instagramButton {
                background: linear-gradient(45deg, #E4405F, #C13584);
                color: white;
                padding: 15px 30px;
                border-radius: 50px;
                text-decoration: none;
                display: inline-flex;
                align-items: center;
                gap: 10px;
                font-weight: 600;
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 10px 20px rgba(228, 64, 95, 0.3);
                }
                
                .innerText {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    
                    i {
                        font-size: 18px;
                    }
                }
                
                .arrows {
                    transition: transform 0.3s ease;
                }
                
                &:hover .arrows {
                    transform: translateX(5px);
                }
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
