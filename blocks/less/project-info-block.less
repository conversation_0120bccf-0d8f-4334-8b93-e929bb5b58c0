// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.projectInfoBlock {
    .backgroundWrapper { 
        width: 80vw;
        position: absolute;
        z-index: -1;
        height: 80vw; 
        .transform(translate(-50%,-50%));
        top: 0;
        opacity: .4;
        left: 0;
        .background {
          opacity: .4;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          .rounded(50%);
          height: 100%; 
          background: @almostWhite;
          -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
          mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
        }
      }
    .cols {
        margin-left: -@vw25;
        width: calc(100% + @vw50);
        &:last-child {
            margin-bottom: -@vw22;
        }
    }
    .col {
        display: inline-block;
        .rounded(@vw50);
        width: calc(33.3333% - @vw50);
        margin: 0 @vw25;
        margin-bottom: @vw22;
        vertical-align: bottom;
        .imageWrapper {
            cursor: pointer;
            width: 100%; 
            height: auto;
            img { 
                cursor: pointer;
                width: 100%;
                object-fit: contain;
                -webkit-filter: blur(@vw10);
                .transitionMore(-webkit-filter, .3s, .45s);
                &.inview {
                    -webkit-filter: blur(0);
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
    .projectInfoBlock {
      .backgroundWrapper {
        width: 80vw;
        height: 80vw;
        opacity: .4;
        .background {
          opacity: .4;
        }
      }
      .cols {
        margin-left: -@vw25-1080;
        width: calc(100% + @vw50-1080);
        &:last-child {
          margin-bottom: -@vw22-1080;
        }
      }
      .col {
        width: calc(33.3333% - @vw50-1080);
        margin: 0 @vw25-1080;
        margin-bottom: @vw22-1080;
        .imageWrapper {
          img {
            -webkit-filter: blur(@vw10-1080);
          }
        }
      }
    }
  }
  
  @media all and (max-width: 580px) {
    .projectInfoBlock {
      .backgroundWrapper {
        width: 80vw;
        height: 80vw;
        opacity: .4;
        .background {
          opacity: .4;
        }
      }
      .cols {
        margin-left: -@vw25-580;
        width: calc(100% + @vw50-580);
        &:last-child {
          margin-bottom: -@vw22-580;
        }
      }
      .text {
        .transform(translate3d(0,0,0)) !important;
      }
      .col {
        width: calc(100% - @vw50-580);
        margin: 0 @vw25-580;
        margin-bottom: @vw22-580;
        .imageWrapper {
          img {
            -webkit-filter: blur(@vw10-580);
          }
        }
      }
    }
  }
  