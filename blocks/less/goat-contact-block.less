.goatContactBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .contactContent {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            
            @media (max-width: 968px) {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .contactInfo {
                .sectionHeader {
                    margin-bottom: 40px;
                    
                    .subTitle {
                        color: #ff6b35;
                        font-size: 1rem;
                        font-weight: 600;
                        text-transform: uppercase;
                        letter-spacing: 1px;
                        margin-bottom: 10px;
                    }
                    
                    .biggerTitle {
                        font-size: 2.5rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 20px;
                        
                        @media (max-width: 768px) {
                            font-size: 2rem;
                        }
                    }
                    
                    .description {
                        font-size: 1.1rem;
                        color: #666;
                        line-height: 1.6;
                    }
                }
                
                .contactDetails {
                    margin-bottom: 40px;
                    
                    .contactItem {
                        display: flex;
                        align-items: flex-start;
                        gap: 15px;
                        margin-bottom: 25px;
                        transition: transform 0.3s ease;
                        
                        .contactIcon {
                            width: 50px;
                            height: 50px;
                            background: #f8f9fa;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            flex-shrink: 0;
                            transition: all 0.3s ease;
                            
                            i {
                                font-size: 20px;
                                color: #666;
                                transition: all 0.3s ease;
                            }
                        }
                        
                        .contactText {
                            h4 {
                                font-size: 1.1rem;
                                font-weight: bold;
                                color: #333;
                                margin-bottom: 5px;
                            }
                            
                            a {
                                color: #ff6b35;
                                text-decoration: none;
                                font-weight: 600;
                                
                                &:hover {
                                    text-decoration: underline;
                                }
                            }
                            
                            p {
                                color: #666;
                                line-height: 1.5;
                                margin: 0;
                            }
                        }
                        
                        &:hover {
                            transform: translateX(5px);
                            
                            .contactIcon {
                                background: #ff6b35;
                                
                                i {
                                    color: white;
                                    transform: scale(1.1);
                                }
                            }
                        }
                        
                        &.animate-in {
                            animation: fadeInLeft 0.6s ease forwards;
                        }
                    }
                }
                
                .socialMedia {
                    h4 {
                        font-size: 1.2rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 20px;
                    }
                    
                    .socialLinks {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 15px;
                        
                        .socialLink {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            padding: 10px 15px;
                            background: #f8f9fa;
                            border-radius: 25px;
                            color: #666;
                            text-decoration: none;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            
                            i {
                                font-size: 16px;
                            }
                            
                            &:hover {
                                background: #ff6b35;
                                color: white;
                                transform: translateY(-2px);
                            }
                            
                            &.animate-in {
                                animation: fadeInUp 0.6s ease forwards;
                            }
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInLeft 0.8s ease forwards;
                }
            }
            
            .contactForm {
                background: #f8f9fa;
                padding: 40px;
                border-radius: 20px;
                
                @media (max-width: 768px) {
                    padding: 30px 20px;
                }
                
                .formTitle {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 30px;
                    text-align: center;
                }
                
                .formWrapper {
                    // Styles for external form plugins
                }
                
                .defaultContactForm {
                    .formGroup {
                        position: relative;
                        margin-bottom: 25px;
                        
                        label {
                            position: absolute;
                            top: 15px;
                            left: 15px;
                            color: #666;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            pointer-events: none;
                            background: white;
                            padding: 0 5px;
                        }
                        
                        input,
                        textarea {
                            width: 100%;
                            padding: 15px;
                            border: 2px solid #e9ecef;
                            border-radius: 10px;
                            font-size: 1rem;
                            transition: all 0.3s ease;
                            background: white;
                            
                            &:focus {
                                outline: none;
                                border-color: #ff6b35;
                                box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
                            }
                        }
                        
                        textarea {
                            resize: vertical;
                            min-height: 120px;
                        }
                        
                        &.focused,
                        &:focus-within {
                            label {
                                top: -8px;
                                left: 10px;
                                font-size: 0.9rem;
                                color: #ff6b35;
                            }
                        }
                        
                        &.error {
                            input,
                            textarea {
                                border-color: #dc3545;
                            }
                            
                            .error-message {
                                color: #dc3545;
                                font-size: 0.8rem;
                                margin-top: 5px;
                                font-weight: 600;
                            }
                        }
                    }
                    
                    .submitBtn {
                        width: 100%;
                        background: linear-gradient(45deg, #ff6b35, #f7931e);
                        color: white;
                        padding: 15px 30px;
                        border-radius: 50px;
                        border: none;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 10px;
                        
                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
                        }
                        
                        &.loading {
                            opacity: 0.7;
                            cursor: not-allowed;
                            
                            .arrows {
                                animation: spin 1s linear infinite;
                            }
                        }
                        
                        .arrows {
                            transition: transform 0.3s ease;
                        }
                        
                        &:hover:not(.loading) .arrows {
                            transform: translateX(5px);
                        }
                    }
                    
                    .form-message {
                        margin-top: 20px;
                        padding: 15px;
                        border-radius: 10px;
                        font-weight: 600;
                        
                        &.success {
                            background: #d4edda;
                            color: #155724;
                            border: 1px solid #c3e6cb;
                        }
                        
                        &.error {
                            background: #f8d7da;
                            color: #721c24;
                            border: 1px solid #f5c6cb;
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInRight 0.8s ease 0.2s forwards;
                    opacity: 0;
                }
            }
        }
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
