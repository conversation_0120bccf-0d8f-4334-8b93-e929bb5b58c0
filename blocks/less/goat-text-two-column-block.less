.goatTextTwoColumnBlock {
    padding: 80px 0;
    background: white;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 60px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .twoColumnContent {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            margin-bottom: 60px;
            
            @media (max-width: 768px) {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .leftColumn,
            .rightColumn {
                .columnTitle {
                    font-size: 1.5rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 20px;
                }
                
                .columnContent {
                    color: #666;
                    line-height: 1.6;
                    margin-bottom: 25px;
                    
                    p {
                        margin-bottom: 15px;
                    }
                    
                    ul, ol {
                        padding-left: 20px;
                        
                        li {
                            margin-bottom: 8px;
                        }
                    }
                }
                
                .columnImage {
                    margin-bottom: 25px;
                    border-radius: 10px;
                    overflow: hidden;
                    
                    img {
                        width: 100%;
                        height: auto;
                        transition: transform 0.3s ease;
                        
                        &:hover {
                            transform: scale(1.02);
                        }
                    }
                }
                
                .columnButton {
                    .button {
                        background: linear-gradient(45deg, #ff6b35, #f7931e);
                        color: white;
                        padding: 12px 25px;
                        border-radius: 50px;
                        text-decoration: none;
                        display: inline-flex;
                        align-items: center;
                        gap: 8px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 8px 16px rgba(255, 107, 53, 0.3);
                        }
                        
                        .arrows {
                            transition: transform 0.3s ease;
                        }
                        
                        &:hover .arrows {
                            transform: translateX(3px);
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInUp 0.6s ease forwards;
                }
            }
        }
        
        .bottomCTA {
            text-align: center;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 20px;
            padding: 50px;
            color: white;
            
            @media (max-width: 768px) {
                padding: 30px 20px;
            }
            
            .ctaTitle {
                font-size: 2rem;
                font-weight: bold;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 1.5rem;
                }
            }
            
            .ctaDescription {
                font-size: 1.1rem;
                margin-bottom: 30px;
                opacity: 0.9;
                max-width: 600px;
                margin-left: auto;
                margin-right: auto;
                line-height: 1.6;
            }
            
            .ctaButton {
                .button {
                    background: white;
                    color: #ff6b35;
                    padding: 15px 30px;
                    border-radius: 50px;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 10px;
                    font-weight: 600;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                    }
                    
                    .arrows {
                        transition: transform 0.3s ease;
                    }
                    
                    &:hover .arrows {
                        transform: translateX(5px);
                    }
                }
            }
            
            &.animate-in {
                animation: fadeInUp 0.6s ease forwards;
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
