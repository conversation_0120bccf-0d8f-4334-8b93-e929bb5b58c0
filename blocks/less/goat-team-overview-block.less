.goatTeamOverviewBlock {
    padding: 80px 0;
    background: #f8f9fa;
    
    .contentWrapper {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
        
        .sectionHeader {
            text-align: center;
            margin-bottom: 40px;
            
            .subTitle {
                color: #ff6b35;
                font-size: 1rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 1px;
                margin-bottom: 10px;
            }
            
            .biggerTitle {
                font-size: 2.5rem;
                font-weight: bold;
                color: #333;
                margin-bottom: 20px;
                
                @media (max-width: 768px) {
                    font-size: 2rem;
                }
            }
            
            .description {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
                line-height: 1.6;
            }
        }
        
        .teamFilters {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 50px;
            flex-wrap: wrap;
            
            .filterBtn {
                padding: 10px 20px;
                border: 2px solid #e9ecef;
                background: white;
                color: #666;
                border-radius: 25px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                
                &:hover {
                    border-color: #ff6b35;
                    color: #ff6b35;
                }
                
                &.active {
                    background: linear-gradient(45deg, #ff6b35, #f7931e);
                    border-color: #ff6b35;
                    color: white;
                }
            }
        }
        
        .teamMembersGrid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
            
            @media (max-width: 768px) {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
            }
            
            .teamMemberCard {
                background: white;
                border-radius: 15px;
                overflow: hidden;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                
                &:hover {
                    transform: translateY(-5px);
                    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
                }
                
                .memberLink {
                    text-decoration: none;
                    color: inherit;
                    display: block;
                }
                
                .memberImage {
                    position: relative;
                    height: 250px;
                    overflow: hidden;
                    
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        transition: transform 0.3s ease;
                    }
                    
                    .placeholderImage {
                        width: 100%;
                        height: 100%;
                        background: #f8f9fa;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #666;
                        font-size: 3rem;
                    }
                    
                    .memberOverlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(255, 107, 53, 0.9);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                        
                        .viewProfile {
                            color: white;
                            font-weight: 600;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            
                            i {
                                font-size: 16px;
                            }
                        }
                    }
                    
                    &:hover {
                        img {
                            transform: scale(1.05);
                        }
                        
                        .memberOverlay {
                            opacity: 1;
                        }
                    }
                }
                
                .memberInfo {
                    padding: 25px;
                    
                    .memberName {
                        font-size: 1.3rem;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 8px;
                    }
                    
                    .memberRole {
                        color: #ff6b35;
                        font-weight: 600;
                        margin-bottom: 15px;
                        font-size: 0.9rem;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                    }
                    
                    .memberBio {
                        color: #666;
                        line-height: 1.5;
                        margin-bottom: 20px;
                        font-size: 0.95rem;
                    }
                    
                    .memberSocials {
                        display: flex;
                        gap: 10px;
                        flex-wrap: wrap;
                        margin-bottom: 15px;
                        
                        .socialLink {
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                            width: 35px;
                            height: 35px;
                            background: #f1f3f4;
                            border-radius: 50%;
                            color: #666;
                            text-decoration: none;
                            transition: all 0.3s ease;
                            transform: translateY(10px) scale(0.8);
                            opacity: 0.7;
                            
                            &:hover {
                                background: #ff6b35;
                                color: white;
                                transform: translateY(-2px) scale(1.1);
                            }
                            
                            i {
                                font-size: 16px;
                            }
                        }
                    }
                    
                    .memberAchievements {
                        .achievement {
                            background: #f8f9fa;
                            padding: 5px 10px;
                            border-radius: 15px;
                            display: inline-block;
                            margin: 2px;
                            
                            .achievementTitle {
                                font-size: 0.8rem;
                                color: #666;
                                font-weight: 600;
                            }
                        }
                    }
                }
                
                &.animate-in {
                    animation: fadeInUp 0.6s ease forwards;
                }
                
                &.fade-in {
                    animation: fadeIn 0.5s ease forwards;
                }
            }
        }
        
        .joinTeamSection {
            text-align: center;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            border-radius: 20px;
            padding: 50px;
            color: white;
            
            @media (max-width: 768px) {
                padding: 30px 20px;
            }
            
            .joinTeamContent {
                .joinTeamTitle {
                    font-size: 2rem;
                    font-weight: bold;
                    margin-bottom: 20px;
                    
                    @media (max-width: 768px) {
                        font-size: 1.5rem;
                    }
                }
                
                .joinTeamDescription {
                    font-size: 1.1rem;
                    margin-bottom: 30px;
                    opacity: 0.9;
                    max-width: 600px;
                    margin-left: auto;
                    margin-right: auto;
                    line-height: 1.6;
                }
                
                .joinTeamCTA {
                    .button {
                        background: white;
                        color: #ff6b35;
                        padding: 15px 30px;
                        border-radius: 50px;
                        text-decoration: none;
                        display: inline-flex;
                        align-items: center;
                        gap: 10px;
                        font-weight: 600;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
                        }
                        
                        .arrows {
                            transition: transform 0.3s ease;
                        }
                        
                        &:hover .arrows {
                            transform: translateX(5px);
                        }
                    }
                }
            }
        }
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
