// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.projectsHomeBlock {
  &.inview {
    .cols {
      .col {
        .buttonWrapper {
          .transform(translateY(0));
          opacity: 1;
          transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
        }
        .project {
          .transform(scale(1), translateY(0));
          opacity: 1;
          transition: opacity 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1), transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
          .stagger(2,.3s,0s);
        }
      }
    }
  }
    .cols {
        margin-left: -@vw25;
        width: calc(100% + @vw50);
    }
    .col {
        display: inline-block;
        .rounded(@vw50);
        width: calc(33.3333% - @vw50);
        margin: 0 @vw25;
        vertical-align: top;
    }
    .col {
        &:nth-child(1) {
            .project {
                .imageWrapper {
                    .innerImage {
                        .paddingRatio(1, 1);
                    }
                }
            }
        }
        &:nth-child(2) {
            margin-top: @vw100;
            .project {
                .imageWrapper {
                    .innerImage {
                        .paddingRatio(461, 366);
                    }
                }
            }
        }
        &:nth-child(3) {
            margin-top: calc(@vw100 + @vw40);
            .project {
                .imageWrapper {
                    .innerImage {
                        .paddingRatio(458, 345);
                    }
                }
            }
        }
        .text {
          text-align: center;
        }
        .buttonWrapper {
          text-align: center;
          margin-top: @vw30;
          margin-bottom: @vw60;
          .transform(translateY(@vw30));
          opacity: 0;
        }
        .project {
            text-decoration: none;
            color: @almostWhite;
            cursor: pointer;
            display: block;
            width: 100%;
            .transform(scale(.8), translateY(@vw60));
            opacity: 0;
            &:hover {
              .imageWrapper {
                clip-path: inset(@vw10);
              }
              .textLink {
                color: @primaryColor;
                .arrows {
                  border-color: @primaryColor;
                  i {
                    &:first-child {
                      .transform(translate(-50%, -50%));
                    }
                    &:last-child {
                      .transform(translate(50%, -150%) scale(.5));
                    }
                  }
                }
              }
            }
            * {
              cursor: pointer;
            }
            .imageWrapper {
                display: block;
                margin-bottom: @vw10;
                width: 100%;
                overflow: hidden;
                position: relative;
                clip-path: inset(0%);
                .transitionMore(clip-path, .45s);
                .innerImage {
                  display: block;
                }
                img {
                    pointer-events: none;
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    object-position: center;
                }
            }
            .innerCols {
              display: block;
              width: 100%;
              white-space: nowrap;
              .innerCol {
                white-space: normal;
              }
            }
            .innerCol {
                display: inline-block;
                vertical-align: middle;
                width: 47%;
                &:last-child{ 
                  width: 53%;
                }
                &:last-child {
                    text-align: right;
                }
                .tinyTitle, .category, .divider {
                  display: inline-block;
                  width: auto;
                }
                .category {
                  text-transform: uppercase;
                }
                .textLink {
                  display: inline-table;
                }
            }
        }
    }
}

@media all and (max-width: 1080px) {
  .projectsHomeBlock {
    .cols {
      margin-left: -@vw25-1080;
      width: calc(100% + @vw50-1080);
    }
    .col {
      width: calc(33.3333% - @vw50-1080);
      margin: 0 @vw25-1080;
    }
    .col {
      &:nth-child(2) {
        margin-top: @vw100-1080;
      }
      &:nth-child(3) {
        margin-top: calc(@vw100-1080 + @vw40-1080);
      }
      .buttonWrapper {
        margin-top: @vw30-1080;
        margin-bottom: @vw60-1080;
        .transform(translateY(@vw30-1080));
      }
      .project {
        .transform(scale(.8), translateY(@vw60-1080));
        .imageWrapper {
          margin-bottom: @vw10-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .projectsHomeBlock {
    .cols {
      margin-left: -@vw25-580;
      width: calc(100% + @vw50-580);
    }
    .col {
      width: calc(100% - @vw50-580);
      margin: 0 @vw25-580;
    }
    .col {
      .transform(translate3d(0,0,0)) !important;
      &:nth-child(1) {
        .project {
          .imageWrapper {
            .innerImage {
              .paddingRatio(1, 1);
            }
          }
        }
      }
      &:nth-child(2) {
        margin-top: @vw120-580;
        .project {
          .imageWrapper {
            .innerImage {
              .paddingRatio(1, 1);
            }
          }
        }
      }
      &:nth-child(3) {
        margin-top: @vw120-580;
        .project {
          .imageWrapper {
            .innerImage {
              .paddingRatio(1, 1);
            }
          }
        }
      }
      .buttonWrapper {
        margin-top: @vw30-580;
        margin-bottom: @vw60-580;
        .transform(translateY(@vw30-580));
      }
      .project {
        .transform(scale(.8), translateY(@vw60-580));
        .imageWrapper {
          margin-bottom: @vw10-580;
        }
        p {
          font-size: @vw16-580;
        }
      }
    }
  }
}
