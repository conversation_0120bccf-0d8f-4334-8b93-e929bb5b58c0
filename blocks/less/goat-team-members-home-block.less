// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.goatTeamMembersHomeBlock {
    .contentWrapper {
        .mediumTitle {
            margin-bottom: @vw80;
            text-align: center;
        }
        .teamMembersGrid {
            display: flex;
            align-items: flex-start;
            gap: @vw16;
            flex-direction: row;
            justify-content: space-around;
            align-items: normal;
            .teamMemberCard {
                background: rgba(255, 255, 255, 0.1);
                .rounded(@vw22);
                overflow: hidden;
                border: 1px solid rgba(255, 255, 255, 0.1);
                width: 25%;
                transition: border 0.3s ease, transform 0.3s ease;
                -webkit-transition: border 0.3s ease, transform 0.3s ease;
                &:before {
                    content: '';
                    position: absolute;
                    top: auto;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 50%;
                    z-index: 1;
                    background: linear-gradient(0deg, rgba(@backgroundColor, 1) 0%, rgba(@backgroundColor, 0) 80%, rgba(@backgroundColor, 0) 100%);
                }
                &:hover {
                    transform: translateY(-10px);
                    border-color: rgba(212, 175, 55, 0.3);
                }

                .memberLink {
                    text-decoration: none;
                    color: inherit;
                    display: block;
                }

                .memberImage {
                    position: relative;
                    overflow: hidden;
                    height: @vw100 * 4.9;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        object-position: top;
                    }
                }

                .memberInfo {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    height: auto;
                    width: 100%;
                    padding: @vw24;
                    z-index: 2;
                    .memberSocials {
                        display: flex;
                        gap: @vw10;
                        .socialLink {
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                            width: @vw20;
                            height: @vw20;
                            color: white;
                            text-decoration: none;
                            transition: all 0.3s ease;
                            border: 1px solid rgba(255, 255, 255, 0.2);
                            &:hover {
                                background: #D4AF37;
                                color: #1a1a1a;
                                .transform(translateY(-3px));
                                border-color: #D4AF37;
                            }

                            i {
                                font-size: 18px;
                            }
                        }
                    }
                }
            }
        }
        .descriptionCard {
            width: 25%;
            display: inline-flex;
            flex-direction: column;
            .rounded(@vw22);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: @vw100 @vw24 @vw50 @vw24;
            .text {
                position: relative;
            }
            .ctaWrapper {
                margin-top: auto;
            }
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                background: linear-gradient(-45deg, rgba(@primaryColor, .1), rgba(@backgroundColor, 0));
                // background: rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(@vw30);
                z-index: 0;
            }
        }
    }
}
