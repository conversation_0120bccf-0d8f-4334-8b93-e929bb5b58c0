// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.goatPartnersTeamEventsBlock {
    .backgroundImage {
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%, -50%));
        width: calc(50% ~"-" @vw64);
        height: auto;
        opacity: .5;
        overflow: hidden;
        animation: blur 5s ease infinite;
        img {
            width: 100%;
            height: auto;
            object-fit: cover;
        }
    }
    .marqueeWrapper {
        position: relative;
        z-index: -1;
    }
    .marquee {
        margin: @vw100 0;
        white-space: nowrap;
        .itemsContainer {
            display: inline-block;
            will-change: transform;
            .item {
                display: inline-block;
                position: relative;
                width: auto;
                vertical-align: middle;
                margin: 0 @vw50;
                img {
                    display: block;
                    width: 100%;
                    height: auto;
                    max-width: @vw100 * 2;
                    max-height: @vw100;
                    object-fit: contain;
                }
            }
        }
    }
    .sectionHeader {
        margin: auto;
        display: block;
        text-align: center;
        .button {
            margin-top: @vw40;
        }
    }
    .col {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        &.right {
            text-align: right;
        }
    }
    .events {
        display: flex;
        margin-top: @vw30;
        gap: @vw16;
        .event {
            color: @hardWhite;
            text-decoration: none;
            display: inline-block;
            width: 33.3333%;
            position: relative;
            cursor: pointer;
            .transitionMore(transform, .3s);
            &:nth-child(2) {
                margin-top: @vw40;
            }
            &:hover {
                .transform(translateY(-@vw10));
                .imageWrapper {
                    .innerImage {
                        .filter(blur(@vw40));
                    }
                }
            }
            * {
                cursor: pointer;
            }
            .imageWrapper {
                height: auto;
                display: block;
                overflow: hidden;
                position: relative;
                .rounded(@vw22);
                &:before {
                    content: '';
                    position: absolute;
                    top: auto;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 50%;
                    z-index: 1;
                    background: linear-gradient(0deg, rgba(@backgroundColor, 1) 0%, rgba(@backgroundColor, 0) 80%, rgba(@backgroundColor, 0) 100%);
                }
                .innerImage {
                    .paddingRatio(1,1);
                    height: 0;
                    width: 100%;
                    position: relative;
                    .transitionMore(filter, .3s);
                }
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }
            }
            .eventInfo {
                position: absolute;
                bottom: @vw50;
                left: 0;
                width: 100%;
                padding: 0 @vw24;
                z-index: 2;
                .smallTitle {
                    margin-bottom: @vw10;
                }
            }
        }
    }
}

@keyframes blur {
    0% {
        .filter(blur(@vw10));
    }
    50% {
        .filter(blur(@vw20));
    }
    100% {
        .filter(blur(@vw10));
    }
}
