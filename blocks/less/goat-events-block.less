// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.goatEventsBlock {
    .sectionHeader {
        margin: auto;
        display: block;
        text-align: center;
        .button {
            margin-top: @vw40;
        }
    }
    .col {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        &.right {
            text-align: right;
        }
    }
    .events {
        display: flex;
        margin-top: @vw30;
        gap: @vw16;
        &:not(:last-child) {
            margin-top: @vw40;
            margin-bottom: @vw100;
        }
        &.upcoming {
            .event {
                &:first-child {
                    width: 66.6666%;
                }
                .imageWrapper {
                    .innerImage {
                        .paddingRatio(1, 0.5);
                    }
                }
            }
        }
        .event {
            color: @hardWhite;
            text-decoration: none;
            display: inline-block;
            width: 33.3333%;
            position: relative;
            cursor: pointer;
            .transitionMore(transform, .3s);
            &:hover {
                .transform(translateY(-@vw10));
                .imageWrapper {
                    .innerImage {
                        .filter(blur(@vw40));
                    }
                }
            }
            * {
                cursor: pointer;
            }
            .imageWrapper {
                height: auto;
                display: block;
                overflow: hidden;
                position: relative;
                .rounded(@vw22);
                &:before {
                    content: '';
                    position: absolute;
                    top: auto;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 50%;
                    z-index: 1;
                    background: linear-gradient(0deg, rgba(@backgroundColor, 1) 0%, rgba(@backgroundColor, 0) 80%, rgba(@backgroundColor, 0) 100%);
                }
                .innerImage {
                    .paddingRatio(1,1);
                    height: 0;
                    width: 100%;
                    position: relative;
                    .transitionMore(filter, .3s);
                }
                img, video {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }
            }
            .eventInfo {
                position: absolute;
                bottom: @vw50;
                left: 0;
                width: 100%;
                padding: 0 @vw24;
                z-index: 2;
                .smallTitle {
                    margin-bottom: @vw10;
                }
            }
        }
    }
}