// out: false
@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.goatPartnersBlock {
    &.inview {
        .sectionHeader {
            opacity: 1;
            .transitionMore(opacity, 0.3s, 0.03s, ease-in-out);
        }
        .partners {
            .partner {
                opacity: 1;
                .transform(translateY(0));
                transition: opacity 0.3s 0.03s ease-in-out, transform 0.6s 0.03s cubic-bezier(0, 0.55, 0.45, 1);
                .stagger(30, 0.15s, .03s);
            }
        }
    }
    .sectionHeader {
        margin-bottom: @vw40;
        opacity: 0;
    }
    .partners {
        flex-direction: row;
        justify-content: space-around;
        align-items: normal;
        .partner {
            cursor: pointer;
            width: 100%;
            display: block;
            position: relative;
            .rounded(@vw22);
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: @vw60 @vw24;
            padding-right: @vw100 * 2;
            color: @hardWhite;
            text-decoration: none;
            opacity: 0;
            .transform(translateY(@vw20));
            &:not(:last-child) {
                margin-bottom: @vw16;
            }
            &:before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1;
                background: linear-gradient(-45deg, rgba(@primaryColor, .1), rgba(@backgroundColor, 0));
                backdrop-filter: blur(@vw30);
                z-index: 0;
                opacity: .9;
            }
            &:hover {
                border-color: rgba(212, 175, 55, 0.3);
            }
            * {
                cursor: pointer;
            }
            .partnerLogo {
                padding: 0 @vw40;
                width: @vw100 * 3;
                display: inline-block;
                vertical-align: middle;
                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    filter: grayscale(100%);
                    transition: filter 0.3s ease;
                }
            }
            .partnerInfo {
                display: inline-block;
                vertical-align: middle;
                width: calc(100% - @vw100 * 3);
                padding-left: @vw24;
            }
        }
    }
}
