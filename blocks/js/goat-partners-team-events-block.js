document.addEventListener('DOMContentLoaded', function() {
    const partnersTeamEventsBlocks = document.querySelectorAll('.goatPartnersTeamEventsBlock');
    
    partnersTeamEventsBlocks.forEach(function(block) {
        initPartnersTeamEvents(block);
    });
    
    function initPartnersTeamEvents(block) {
        const partnerCards = block.querySelectorAll('.partnerCard');
        const teamCards = block.querySelectorAll('.teamMemberCard');
        const eventCards = block.querySelectorAll('.eventCard');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Animate partner cards
        partnerCards.forEach(function(card, index) {
            card.style.animationDelay = `${index * 0.1}s`;
            observer.observe(card);
            
            // Partner logo hover effect
            const logo = card.querySelector('.partnerLogo img');
            if (logo) {
                card.addEventListener('mouseenter', function() {
                    logo.style.filter = 'grayscale(0%) brightness(1.1)';
                    logo.style.transform = 'scale(1.05)';
                });
                
                card.addEventListener('mouseleave', function() {
                    logo.style.filter = 'grayscale(100%)';
                    logo.style.transform = 'scale(1)';
                });
            }
        });
        
        // Animate team cards
        teamCards.forEach(function(card, index) {
            card.style.animationDelay = `${(index + partnerCards.length) * 0.1}s`;
            observer.observe(card);
        });
        
        // Animate event cards
        eventCards.forEach(function(card, index) {
            card.style.animationDelay = `${(index + partnerCards.length + teamCards.length) * 0.1}s`;
            observer.observe(card);
            
            // Event card hover effect
            card.addEventListener('mouseenter', function() {
                const eventImage = this.querySelector('.eventImage img');
                if (eventImage) {
                    eventImage.style.transform = 'scale(1.05)';
                }
            });
            
            card.addEventListener('mouseleave', function() {
                const eventImage = this.querySelector('.eventImage img');
                if (eventImage) {
                    eventImage.style.transform = 'scale(1)';
                }
            });
        });
    }
});
