document.addEventListener('DOMContentLoaded', function() {
    const headerBlocks = document.querySelectorAll('.goatHeaderBlock');
    
    headerBlocks.forEach(function(block) {
        initHeaderBlock(block);
    });
    
    function initHeaderBlock(block) {
        const headerContent = block.querySelector('.headerContent');
        
        // Parallax effect on scroll
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.3;
            
            const bgImage = block.querySelector('.bgImage');
            if (bgImage) {
                bgImage.style.transform = `translateY(${rate}px)`;
            }
        });
        
        // Animate content on load
        if (headerContent) {
            setTimeout(function() {
                headerContent.classList.add('animate-in');
            }, 300);
        }
        
        // Breadcrumb navigation
        const breadcrumbLinks = block.querySelectorAll('.breadcrumbs a');
        breadcrumbLinks.forEach(function(link) {
            link.addEventListener('click', function(e) {
                // Add smooth scroll if linking to anchor
                const href = this.getAttribute('href');
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });
    }
});
