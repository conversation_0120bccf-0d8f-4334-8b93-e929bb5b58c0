document.addEventListener('DOMContentLoaded', function() {
    const partnersBlocks = document.querySelectorAll('.goatPartnersBlock');
    
    partnersBlocks.forEach(function(block) {
        initPartnersBlock(block);
    });
    
    function initPartnersBlock(block) {
        const partnerCards = block.querySelectorAll('.partnerCard');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Initialize partner cards
        partnerCards.forEach(function(card, index) {
            card.style.animationDelay = `${index * 0.1}s`;
            observer.observe(card);
            
            // Partner logo hover effects
            const logo = card.querySelector('.partnerLogo img');
            if (logo) {
                card.addEventListener('mouseenter', function() {
                    logo.style.filter = 'grayscale(0%) brightness(1.1)';
                    logo.style.transform = 'scale(1.05)';
                    this.style.borderColor = '#ff6b35';
                });
                
                card.addEventListener('mouseleave', function() {
                    logo.style.filter = 'grayscale(100%)';
                    logo.style.transform = 'scale(1)';
                    this.style.borderColor = '#e9ecef';
                });
            }
            
            // Add click tracking for analytics
            card.addEventListener('click', function() {
                const partnerName = this.querySelector('.partnerName h3');
                if (partnerName) {
                    console.log('Partner clicked:', partnerName.textContent);
                    // Add analytics tracking here
                }
            });
        });
        
        // Become partner section animation
        const becomePartnerSection = block.querySelector('.becomePartnerSection');
        if (becomePartnerSection) {
            observer.observe(becomePartnerSection);
            
            // Add floating animation
            setInterval(function() {
                if (becomePartnerSection.classList.contains('animate-in')) {
                    becomePartnerSection.style.transform = 'translateY(-5px)';
                    setTimeout(function() {
                        becomePartnerSection.style.transform = 'translateY(0)';
                    }, 1000);
                }
            }, 3000);
        }
    }
});
