document.addEventListener('DOMContentLoaded', function() {
    const instagramBlocks = document.querySelectorAll('.goatInstagramEmbedBlock');
    
    instagramBlocks.forEach(function(block) {
        initInstagramEmbed(block);
    });
    
    function initInstagramEmbed(block) {
        const hashtagFeed = block.querySelector('.instagramHashtagFeed');
        const manualFeed = block.querySelector('.instagramManualFeed');
        const embedCode = block.querySelector('.instagramEmbed');
        
        // Initialize hashtag feed if present
        if (hashtagFeed) {
            const hashtag = hashtagFeed.getAttribute('data-hashtag');
            if (hashtag) {
                loadHashtagFeed(hashtagFeed, hashtag);
            }
        }
        
        // Initialize manual feed animations
        if (manualFeed) {
            initManualFeedAnimations(manualFeed);
        }
        
        // Initialize embed code
        if (embedCode) {
            // Process Instagram embed scripts if needed
            processInstagramEmbeds(embedCode);
        }
    }
    
    function loadHashtagFeed(container, hashtag) {
        // This would typically connect to Instagram API
        // For now, show placeholder
        const placeholder = container.querySelector('.instagramPlaceholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <div class="instagram-loading">
                    <p>Loading posts for #${hashtag}...</p>
                    <div class="loading-spinner"></div>
                </div>
            `;
            
            // Simulate loading
            setTimeout(function() {
                placeholder.innerHTML = `
                    <p>Instagram feed for #${hashtag} would appear here.</p>
                    <p><small>Note: Requires Instagram API integration</small></p>
                `;
            }, 2000);
        }
    }
    
    function initManualFeedAnimations(feed) {
        const posts = feed.querySelectorAll('.instagramPost');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        posts.forEach(function(post, index) {
            post.style.animationDelay = `${index * 0.1}s`;
            observer.observe(post);
            
            // Hover effects
            post.addEventListener('mouseenter', function() {
                const image = this.querySelector('.postImage img');
                const icon = this.querySelector('.instagramIcon');
                
                if (image) {
                    image.style.transform = 'scale(1.05)';
                }
                if (icon) {
                    icon.style.opacity = '1';
                    icon.style.transform = 'scale(1.2)';
                }
            });
            
            post.addEventListener('mouseleave', function() {
                const image = this.querySelector('.postImage img');
                const icon = this.querySelector('.instagramIcon');
                
                if (image) {
                    image.style.transform = 'scale(1)';
                }
                if (icon) {
                    icon.style.opacity = '0.8';
                    icon.style.transform = 'scale(1)';
                }
            });
        });
    }
    
    function processInstagramEmbeds(container) {
        // Process Instagram embed scripts
        const scripts = container.querySelectorAll('script');
        scripts.forEach(function(script) {
            if (script.src && script.src.includes('instagram.com')) {
                // Reload Instagram embed script
                const newScript = document.createElement('script');
                newScript.src = script.src;
                newScript.async = true;
                document.head.appendChild(newScript);
            }
        });
        
        // Trigger Instagram embed processing if available
        if (window.instgrm && window.instgrm.Embeds) {
            window.instgrm.Embeds.process();
        }
    }
});
