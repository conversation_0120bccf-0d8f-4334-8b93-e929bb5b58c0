document.addEventListener('DOMContentLoaded', function() {
    const teamMembersHomeBlocks = document.querySelectorAll('.goatTeamMembersHomeBlock');
    
    teamMembersHomeBlocks.forEach(function(block) {
        initTeamMembersHome(block);
    });
    
    function initTeamMembersHome(block) {
        const teamCards = block.querySelectorAll('.teamMemberCard');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Initialize team cards
        teamCards.forEach(function(card, index) {
            card.style.animationDelay = `${index * 0.1}s`;
            observer.observe(card);
            
            // Add hover effects
            initCardHoverEffects(card);
        });
    }
    
    function initCardHoverEffects(card) {
        const memberImage = card.querySelector('.memberImage');
        const socialLinks = card.querySelectorAll('.socialLink');
        
        card.addEventListener('mouseenter', function() {
            this.classList.add('hover');
            
            // Animate social links
            socialLinks.forEach(function(link, index) {
                setTimeout(function() {
                    link.style.transform = 'translateY(0) scale(1)';
                    link.style.opacity = '1';
                }, index * 50);
            });
        });
        
        card.addEventListener('mouseleave', function() {
            this.classList.remove('hover');
            
            // Reset social links
            socialLinks.forEach(function(link) {
                link.style.transform = 'translateY(10px) scale(0.8)';
                link.style.opacity = '0.7';
            });
        });
        
        // Individual social link hover
        socialLinks.forEach(function(link) {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.2)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }
});
