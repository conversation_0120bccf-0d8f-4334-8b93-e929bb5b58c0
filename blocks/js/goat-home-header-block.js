$(document).ready(function(){
    $(document).on("initPage", function () {
        if ($(".goatHomeHeaderBlock").length > 0){
            initializeHomeHeaderBlock();
        }
    });
});

function initializeHomeHeaderBlock() {
   $(".goatHomeHeaderBlock .svgBackground svg").addClass("animate");
   ScrollTrigger.create({
        trigger: $(".goatHomeHeaderBlock"),
        start: "top top",
        end: "bottom top",
        onUpdate: function(e) {
            gsap.set(".goatHomeHeaderBlock .headerBackground", {
                top: scrollY / 2,
                scale: 1 + (e.progress / 6),
                // skewX: e.progress * 10 + "deg",
                // skewY: -e.progress * 20 + "deg"
            });
            gsap.to(".goatHomeHeaderBlock .contentWrapper", {
                yPercent: -(e.progress * 50),
                opacity: 1 - (e.progress * 1.5),
                duration: 0.3,
                ease: "power2.out"
            });
        }
    });
    animateFloatingImageOnMouse();
}

function animateFloatingImageOnMouse() {
    const floatingImage = $(".goatHomeHeaderBlock .floatingImage img");
    $(document).off("mousemove", ".goatHomeHeaderBlock").on("mousemove", ".goatHomeHeaderBlock", function(e) {
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;
        gsap.to(floatingImage, {
            rotateY: (x - 0.5) * 40,
            rotateX: (y - 0.5) * 10,
            transformOrigin: "center center",
            duration: 0.3,
            ease: "power2.out"
        });
    });
}