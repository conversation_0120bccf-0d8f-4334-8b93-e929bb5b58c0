document.addEventListener('DOMContentLoaded', function() {
    const threeEventsBlocks = document.querySelectorAll('.goatThreeEventsBlock');
    
    threeEventsBlocks.forEach(function(block) {
        initThreeEventsBlock(block);
    });
    
    function initThreeEventsBlock(block) {
        const eventCards = block.querySelectorAll('.eventCard');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Initialize event cards
        eventCards.forEach(function(card, index) {
            card.style.animationDelay = `${index * 0.2}s`;
            observer.observe(card);
            
            // Event card hover effects
            card.addEventListener('mouseenter', function() {
                const eventImage = this.querySelector('.eventImage img');
                const dateBadge = this.querySelector('.eventDateBadge');
                const readMore = this.querySelector('.readMore');
                
                if (eventImage) {
                    eventImage.style.transform = 'scale(1.05)';
                }
                if (dateBadge) {
                    dateBadge.style.transform = 'scale(1.1)';
                }
                if (readMore) {
                    readMore.style.transform = 'translateX(5px)';
                }
                
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 15px 30px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                const eventImage = this.querySelector('.eventImage img');
                const dateBadge = this.querySelector('.eventDateBadge');
                const readMore = this.querySelector('.readMore');
                
                if (eventImage) {
                    eventImage.style.transform = 'scale(1)';
                }
                if (dateBadge) {
                    dateBadge.style.transform = 'scale(1)';
                }
                if (readMore) {
                    readMore.style.transform = 'translateX(0)';
                }
                
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
            });
            
            // Add click tracking
            card.addEventListener('click', function() {
                const eventTitle = this.querySelector('.eventTitle');
                if (eventTitle) {
                    console.log('Event clicked:', eventTitle.textContent);
                    // Add analytics tracking here
                }
            });
        });
        
        // Date badge animations
        const dateBadges = block.querySelectorAll('.eventDateBadge');
        dateBadges.forEach(function(badge) {
            // Add pulsing animation for upcoming events
            const eventCard = badge.closest('.eventCard');
            const eventDate = eventCard.querySelector('.eventDate span');
            
            if (eventDate) {
                const dateText = eventDate.textContent;
                const eventDateObj = new Date(dateText);
                const now = new Date();
                
                if (eventDateObj > now) {
                    badge.classList.add('upcoming');
                    setInterval(function() {
                        badge.style.transform = 'scale(1.05)';
                        setTimeout(function() {
                            badge.style.transform = 'scale(1)';
                        }, 500);
                    }, 2000);
                }
            }
        });
    }
});
