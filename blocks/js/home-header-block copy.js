var autoSlide;
$(document).ready(function () {
  $(document).on("initPage", function () {
      if (autoSlide) autoSlide.kill();
      if ($(".homeHeaderBlock").length > 0){
        initializeHomeHeaderBlock();
      }
  });
});


function initializeHomeHeaderBlock() {
  var currentIndex = 0;
  var slides = $(".homeHeaderBlock .projectSlider .slide");
  var totalSlides = slides.length;
  var hoverEffects = [];
  var direction = "next";

  var heighestTitleSlide = 0;

  $(".homeHeaderBlock .projectSlider .titleSlide").each(function (index, slide) {
    var titleHeight = $(slide).outerHeight();
    if (titleHeight > heighestTitleSlide) {
      heighestTitleSlide = titleHeight;
    }
  });

  $(".homeHeaderBlock .projectSlider .titleSlider").css("height", heighestTitleSlide);

  function startAutoSlide() {
    if (autoSlide) autoSlide.kill();
    autoSlide = gsap.timeline({ repeat: -1 });
    autoSlide.to({}, { duration: 5, onComplete: function () {
      nextSlide(currentIndex, totalSlides);
    }});
  }

  function nextSlide() {
    direction = "next";
    currentIndex = (currentIndex + 1) % totalSlides;
    showSlide(currentIndex, false);
    hoverEffects.forEach((effect) => effect.next());
    startAutoSlide();
  }
  

  function showSlide(index, first=false) {
    updateHoverEffect(index);
    updateActiveTitleSlide(index, first);
  }

  function updateHoverEffect(index) {
    var imageWrappers = $(".homeHeaderBlock .imageWrapper");

    imageWrappers.each(function (wrapperIndex, wrapper) {
      var image1, image2, path;
      if ($(window).outerWidth(true,true) <= 580) {
        path = 'mobile';
      } else {
        path = 'large';
      }
      if (direction === "next") {
        image1 = projectImages[index][path];
        image2 = projectImages[(index + 1) % totalSlides][path];
      } else {
        image1 = projectImages[index][path];
        image2 = projectImages[(index - 1 + totalSlides) % totalSlides][path];
      }

      $(wrapper).addClass('invisible');
      $(wrapper).find('canvas').remove();

      hoverEffects[wrapperIndex] = new hoverEffect({
        parent: wrapper,
        intensity: 0.7,
        speedIn: 1.2,
        speedOut: 0.9,
        image1: image1,
        image2: image2,
        imagesRatio: 992 / 1020,
        displacementImage: currentDisplacementImage,
        hover: false,
      });

      setTimeout(function() {
        $(wrapper).removeClass('invisible');
      }, 200);
    });
  }

  function updateActiveTitleSlide(index, first) {
    if (index == totalSlides -1) {
      index = 0;
    } else if(first) {
      index = 0;
    } else {
      index = index + 1;
    }
    $(".homeHeaderBlock .projectSlider .titleSlide").removeClass("active");
    $(".homeHeaderBlock .projectSlider .titleSlide").eq(index).addClass("active");
  }

  $(".homeHeaderBlock .next").click(function () {
    nextSlide(currentIndex, totalSlides);
  });

  $(".homeHeaderBlock .prev").click(function () {
    direction = "prev";
    currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
    showSlide(currentIndex, false);
    hoverEffects.forEach((effect) => effect.previous());
    startAutoSlide();
  });

  showSlide(currentIndex, true);
  startAutoSlide();
}

