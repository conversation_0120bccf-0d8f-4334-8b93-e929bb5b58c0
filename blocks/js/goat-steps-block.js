document.addEventListener('DOMContentLoaded', function() {
    const stepsBlocks = document.querySelectorAll('.goatStepsBlock');
    
    stepsBlocks.forEach(function(block) {
        initStepsBlock(block);
    });
    
    function initStepsBlock(block) {
        const stepItems = block.querySelectorAll('.stepItem');
        const bottomCTA = block.querySelector('.bottomCTA');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Animate step connector
                    const connector = entry.target.querySelector('.stepConnector');
                    if (connector) {
                        setTimeout(function() {
                            connector.classList.add('animate-connector');
                        }, 500);
                    }
                }
            });
        }, observerOptions);
        
        // Initialize step items
        stepItems.forEach(function(step, index) {
            step.style.animationDelay = `${index * 0.3}s`;
            observer.observe(step);
            
            // Add hover effects
            step.addEventListener('mouseenter', function() {
                const stepNumber = this.querySelector('.stepNumber');
                const stepIcon = this.querySelector('.stepIcon img');
                const stepImage = this.querySelector('.stepImage img');
                
                if (stepNumber) {
                    stepNumber.style.transform = 'scale(1.1)';
                    stepNumber.style.background = 'linear-gradient(45deg, #ff6b35, #f7931e)';
                }
                if (stepIcon) {
                    stepIcon.style.transform = 'scale(1.1) rotate(5deg)';
                }
                if (stepImage) {
                    stepImage.style.transform = 'scale(1.05)';
                }
                
                this.style.transform = 'translateY(-5px)';
            });
            
            step.addEventListener('mouseleave', function() {
                const stepNumber = this.querySelector('.stepNumber');
                const stepIcon = this.querySelector('.stepIcon img');
                const stepImage = this.querySelector('.stepImage img');
                
                if (stepNumber) {
                    stepNumber.style.transform = 'scale(1)';
                    stepNumber.style.background = '';
                }
                if (stepIcon) {
                    stepIcon.style.transform = 'scale(1) rotate(0deg)';
                }
                if (stepImage) {
                    stepImage.style.transform = 'scale(1)';
                }
                
                this.style.transform = 'translateY(0)';
            });
            
            // Add click functionality for step buttons
            const stepButton = step.querySelector('.stepButton a');
            if (stepButton) {
                stepButton.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    if (href && href.startsWith('#')) {
                        e.preventDefault();
                        const target = document.querySelector(href);
                        if (target) {
                            target.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }
                    }
                });
            }
        });
        
        // Animate bottom CTA
        if (bottomCTA) {
            bottomCTA.style.animationDelay = `${stepItems.length * 0.3}s`;
            observer.observe(bottomCTA);
        }
        
        // Progressive step highlighting on scroll
        window.addEventListener('scroll', function() {
            const blockRect = block.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            
            if (blockRect.top < windowHeight && blockRect.bottom > 0) {
                const scrollProgress = Math.max(0, Math.min(1, 
                    (windowHeight - blockRect.top) / (windowHeight + blockRect.height)
                ));
                
                const activeStepIndex = Math.floor(scrollProgress * stepItems.length);
                
                stepItems.forEach(function(step, index) {
                    if (index <= activeStepIndex) {
                        step.classList.add('active');
                    } else {
                        step.classList.remove('active');
                    }
                });
            }
        });
    }
});
