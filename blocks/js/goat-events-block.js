document.addEventListener('DOMContentLoaded', function() {
    const eventsBlocks = document.querySelectorAll('.goatEventsBlock');
    
    eventsBlocks.forEach(function(block) {
        initEventsBlock(block);
    });
    
    function initEventsBlock(block) {
        const filterBtns = block.querySelectorAll('.filterBtn');
        const eventCards = block.querySelectorAll('.eventCard');
        const loadMoreBtn = block.querySelector('.loadMoreBtn');
        
        // Initialize event filters
        if (filterBtns.length > 0) {
            initEventFilters(block, filterBtns, eventCards);
        }
        
        // Initialize load more functionality
        if (loadMoreBtn) {
            initLoadMore(loadMoreBtn);
        }
        
        // Initialize event card animations
        initEventCardAnimations(eventCards);
    }
    
    function initEventFilters(block, filterBtns, eventCards) {
        filterBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterBtns.forEach(function(b) {
                    b.classList.remove('active');
                });
                this.classList.add('active');
                
                // Filter event cards
                eventCards.forEach(function(card) {
                    const category = card.getAttribute('data-category');
                    
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                        setTimeout(function() {
                            card.classList.add('fade-in');
                        }, 100);
                    } else {
                        card.classList.remove('fade-in');
                        setTimeout(function() {
                            card.style.display = 'none';
                        }, 300);
                    }
                });
                
                // Update grid layout
                setTimeout(function() {
                    const grid = block.querySelector('.eventsGrid');
                    if (grid) {
                        grid.style.display = 'none';
                        grid.offsetHeight; // Trigger reflow
                        grid.style.display = 'grid';
                    }
                }, 350);
            });
        });
    }
    
    function initLoadMore(loadMoreBtn) {
        loadMoreBtn.addEventListener('click', function() {
            const postType = this.getAttribute('data-post-type');
            const postsPerPage = this.getAttribute('data-posts-per-page') || 6;
            const currentPage = parseInt(this.getAttribute('data-current-page') || '1');
            const nextPage = currentPage + 1;
            
            // Show loading state
            this.classList.add('loading');
            this.querySelector('.innerText').textContent = 'Loading...';
            
            // Simulate AJAX request (replace with actual AJAX)
            setTimeout(function() {
                // Reset loading state
                loadMoreBtn.classList.remove('loading');
                loadMoreBtn.querySelector('.innerText').textContent = 'Load More Events';
                
                // Update page number
                loadMoreBtn.setAttribute('data-current-page', nextPage);
                
                // Hide button after 3 loads (simulate no more content)
                if (nextPage >= 4) {
                    loadMoreBtn.style.display = 'none';
                }
            }, 2000);
        });
    }
    
    function initEventCardAnimations(eventCards) {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        eventCards.forEach(function(card, index) {
            card.style.animationDelay = `${index * 0.1}s`;
            observer.observe(card);
            
            // Event card hover effects
            card.addEventListener('mouseenter', function() {
                const eventImage = this.querySelector('.eventImage img');
                const statusBadge = this.querySelector('.statusBadge');
                const readMore = this.querySelector('.readMore');
                
                if (eventImage) {
                    eventImage.style.transform = 'scale(1.05)';
                }
                if (statusBadge) {
                    statusBadge.style.transform = 'scale(1.1)';
                }
                if (readMore) {
                    readMore.style.transform = 'translateX(5px)';
                }
                
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                const eventImage = this.querySelector('.eventImage img');
                const statusBadge = this.querySelector('.statusBadge');
                const readMore = this.querySelector('.readMore');
                
                if (eventImage) {
                    eventImage.style.transform = 'scale(1)';
                }
                if (statusBadge) {
                    statusBadge.style.transform = 'scale(1)';
                }
                if (readMore) {
                    readMore.style.transform = 'translateX(0)';
                }
                
                this.style.transform = 'translateY(0)';
            });
        });
    }
});
