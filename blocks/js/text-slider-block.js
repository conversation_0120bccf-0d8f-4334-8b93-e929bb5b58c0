$(document).ready(function(){
    $(document).on("initPage", function () {
      if ($(".textSliderBlock").length > 0) {
          initTextSliderBlock();
      }
    });
});

function initTextSliderBlock() {
  var slideWidth = $(".textSliderBlock .slide:first").outerWidth(true);
  $(".textSliderBlock .slider").css("width", slideWidth * 2);

  gsap.to('.textSliderBlock .slider', {
    x: -100 + "%",
    easing: 'ease-in-out',
    scrollTrigger: {
      trigger: ".textSliderBlock",
      start: "top bottom",
      end: 'bottom top',
      scrub: true,
    }
  });

  scroller.on("scroll", function(e){
    gsap.to('.textSliderBlock .slider', {
      skewX: e.velocity * 0.2
    });
  });
}
