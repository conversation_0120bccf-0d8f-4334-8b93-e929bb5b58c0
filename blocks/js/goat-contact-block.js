document.addEventListener('DOMContentLoaded', function() {
    const contactBlocks = document.querySelectorAll('.goatContactBlock');
    
    contactBlocks.forEach(function(block) {
        initContactBlock(block);
    });
    
    function initContactBlock(block) {
        const contactInfo = block.querySelector('.contactInfo');
        const contactForm = block.querySelector('.contactForm');
        const contactItems = block.querySelectorAll('.contactItem');
        const socialLinks = block.querySelectorAll('.socialLink');
        const defaultForm = block.querySelector('.defaultContactForm');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Animate contact info and form
        if (contactInfo) {
            contactInfo.style.animationDelay = '0s';
            observer.observe(contactInfo);
        }
        
        if (contactForm) {
            contactForm.style.animationDelay = '0.2s';
            observer.observe(contactForm);
        }
        
        // Animate contact items
        contactItems.forEach(function(item, index) {
            item.style.animationDelay = `${index * 0.1}s`;
            observer.observe(item);
            
            // Add hover effects
            item.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.contactIcon i');
                if (icon) {
                    icon.style.transform = 'scale(1.2) rotate(5deg)';
                    icon.style.color = '#ff6b35';
                }
                this.style.transform = 'translateX(5px)';
            });
            
            item.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.contactIcon i');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                    icon.style.color = '';
                }
                this.style.transform = 'translateX(0)';
            });
        });
        
        // Animate social links
        socialLinks.forEach(function(link, index) {
            link.style.animationDelay = `${index * 0.05}s`;
            observer.observe(link);
            
            // Add hover effects
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.1)';
                this.style.background = '#ff6b35';
                this.style.color = 'white';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.background = '';
                this.style.color = '';
            });
        });
        
        // Handle default contact form
        if (defaultForm) {
            initDefaultContactForm(defaultForm);
        }
    }
    
    function initDefaultContactForm(form) {
        const inputs = form.querySelectorAll('input, textarea');
        const submitBtn = form.querySelector('.submitBtn');
        
        // Add floating label effects
        inputs.forEach(function(input) {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
            
            // Check if input has value on load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
        
        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            submitBtn.classList.add('loading');
            submitBtn.querySelector('.innerText').textContent = 'Sending...';
            
            // Simulate form submission
            setTimeout(function() {
                // Reset form
                form.reset();
                inputs.forEach(function(input) {
                    input.parentElement.classList.remove('focused');
                });
                
                // Reset button
                submitBtn.classList.remove('loading');
                submitBtn.querySelector('.innerText').textContent = 'Send Message';
                
                // Show success message
                showFormMessage('Message sent successfully!', 'success');
            }, 2000);
        });
        
        // Form validation
        inputs.forEach(function(input) {
            input.addEventListener('blur', function() {
                validateField(this);
            });
        });
    }
    
    function validateField(field) {
        const value = field.value.trim();
        const fieldGroup = field.parentElement;
        
        // Remove existing error
        fieldGroup.classList.remove('error');
        const existingError = fieldGroup.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Validate required fields
        if (field.hasAttribute('required') && !value) {
            showFieldError(fieldGroup, 'This field is required');
            return false;
        }
        
        // Validate email
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                showFieldError(fieldGroup, 'Please enter a valid email address');
                return false;
            }
        }
        
        return true;
    }
    
    function showFieldError(fieldGroup, message) {
        fieldGroup.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        fieldGroup.appendChild(errorDiv);
    }
    
    function showFormMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `form-message ${type}`;
        messageDiv.textContent = message;
        
        const form = document.querySelector('.defaultContactForm');
        if (form) {
            form.appendChild(messageDiv);
            
            setTimeout(function() {
                messageDiv.remove();
            }, 5000);
        }
    }
});
