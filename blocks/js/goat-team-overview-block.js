document.addEventListener('DOMContentLoaded', function() {
    const teamOverviewBlocks = document.querySelectorAll('.goatTeamOverviewBlock');
    
    teamOverviewBlocks.forEach(function(block) {
        initTeamFilters(block);
        initTeamAnimations(block);
    });
    
    function initTeamFilters(block) {
        const filterBtns = block.querySelectorAll('.filterBtn');
        const teamCards = block.querySelectorAll('.teamMemberCard');
        
        if (!filterBtns.length) return;
        
        filterBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterBtns.forEach(function(b) {
                    b.classList.remove('active');
                });
                this.classList.add('active');
                
                // Filter team cards
                teamCards.forEach(function(card) {
                    const category = card.getAttribute('data-category');
                    
                    if (filter === 'all' || category === filter) {
                        card.style.display = 'block';
                        card.classList.add('fade-in');
                    } else {
                        card.style.display = 'none';
                        card.classList.remove('fade-in');
                    }
                });
                
                // Trigger layout recalculation
                setTimeout(function() {
                    const grid = block.querySelector('.teamMembersGrid');
                    if (grid) {
                        grid.style.display = 'none';
                        grid.offsetHeight; // Trigger reflow
                        grid.style.display = 'grid';
                    }
                }, 100);
            });
        });
    }
    
    function initTeamAnimations(block) {
        const teamCards = block.querySelectorAll('.teamMemberCard');
        
        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        teamCards.forEach(function(card, index) {
            // Add animation delay based on index
            card.style.animationDelay = `${index * 0.1}s`;
            observer.observe(card);
            
            // Add hover effects
            initCardHoverEffects(card);
        });
    }
    
    function initCardHoverEffects(card) {
        const memberImage = card.querySelector('.memberImage');
        const memberOverlay = card.querySelector('.memberOverlay');
        const socialLinks = card.querySelectorAll('.socialLink');
        
        if (memberImage) {
            card.addEventListener('mouseenter', function() {
                if (memberOverlay) {
                    memberOverlay.style.opacity = '1';
                }
                
                // Animate social links
                socialLinks.forEach(function(link, index) {
                    setTimeout(function() {
                        link.style.transform = 'translateY(0) scale(1)';
                        link.style.opacity = '1';
                    }, index * 50);
                });
            });
            
            card.addEventListener('mouseleave', function() {
                if (memberOverlay) {
                    memberOverlay.style.opacity = '0';
                }
                
                // Reset social links
                socialLinks.forEach(function(link) {
                    link.style.transform = 'translateY(10px) scale(0.8)';
                    link.style.opacity = '0.7';
                });
            });
        }
        
        // Social link hover effects
        socialLinks.forEach(function(link) {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.1)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }
    
    // Load more functionality (if needed)
    const loadMoreBtns = document.querySelectorAll('.loadMoreBtn[data-post-type="team_member"]');
    
    loadMoreBtns.forEach(function(btn) {
        btn.addEventListener('click', function() {
            const postType = this.getAttribute('data-post-type');
            const postsPerPage = this.getAttribute('data-posts-per-page') || 6;
            const currentPage = parseInt(this.getAttribute('data-current-page') || '1');
            const nextPage = currentPage + 1;
            
            // Show loading state
            this.classList.add('loading');
            this.querySelector('.innerText').textContent = 'Loading...';
            
            // AJAX request to load more team members
            const formData = new FormData();
            formData.append('action', 'load_more_posts');
            formData.append('post_type', postType);
            formData.append('posts_per_page', postsPerPage);
            formData.append('paged', nextPage);
            formData.append('nonce', goat_ajax.nonce);
            
            fetch(goat_ajax.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.html) {
                    // Append new team members
                    const grid = document.querySelector('.teamMembersGrid');
                    if (grid) {
                        grid.insertAdjacentHTML('beforeend', data.data.html);
                        
                        // Initialize new cards
                        const newCards = grid.querySelectorAll('.teamMemberCard:not(.initialized)');
                        newCards.forEach(function(card) {
                            card.classList.add('initialized');
                            initCardHoverEffects(card);
                        });
                    }
                    
                    // Update button state
                    this.setAttribute('data-current-page', nextPage);
                    
                    if (!data.data.has_more) {
                        this.style.display = 'none';
                    }
                } else {
                    this.style.display = 'none';
                }
                
                // Reset loading state
                this.classList.remove('loading');
                this.querySelector('.innerText').textContent = 'Load More Team Members';
            })
            .catch(error => {
                console.error('Error loading more team members:', error);
                this.classList.remove('loading');
                this.querySelector('.innerText').textContent = 'Load More Team Members';
            });
        });
    });
});
