document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.goatBigImageSliderBlock .bigImageSlider');
    
    sliders.forEach(function(slider) {
        initSlider(slider);
    });
    
    function initSlider(slider) {
        const sliderWrapper = slider.querySelector('.sliderWrapper');
        const slides = slider.querySelectorAll('.slide');
        const prevBtn = slider.querySelector('.sliderArrow.prev');
        const nextBtn = slider.querySelector('.sliderArrow.next');
        const dots = slider.querySelectorAll('.dot');
        
        if (!slides.length) return;
        
        let currentSlide = 0;
        const totalSlides = slides.length;
        
        // Initialize slider
        updateSlider();
        
        // Auto-play functionality
        let autoPlayInterval;
        const autoPlayDelay = 5000; // 5 seconds
        
        function startAutoPlay() {
            autoPlayInterval = setInterval(function() {
                nextSlide();
            }, autoPlayDelay);
        }
        
        function stopAutoPlay() {
            clearInterval(autoPlayInterval);
        }
        
        // Start auto-play
        startAutoPlay();
        
        // Pause auto-play on hover
        slider.addEventListener('mouseenter', stopAutoPlay);
        slider.addEventListener('mouseleave', startAutoPlay);
        
        // Navigation functions
        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateSlider();
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateSlider();
        }
        
        function goToSlide(index) {
            currentSlide = index;
            updateSlider();
        }
        
        function updateSlider() {
            // Update slides position
            const translateX = -currentSlide * 100;
            sliderWrapper.style.transform = `translateX(${translateX}%)`;
            
            // Update dots
            dots.forEach(function(dot, index) {
                dot.classList.toggle('active', index === currentSlide);
            });
            
            // Update slides visibility for animations
            slides.forEach(function(slide, index) {
                slide.classList.toggle('active', index === currentSlide);
            });
        }
        
        // Event listeners
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                prevSlide();
                stopAutoPlay();
                startAutoPlay(); // Restart auto-play
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                nextSlide();
                stopAutoPlay();
                startAutoPlay(); // Restart auto-play
            });
        }
        
        // Dot navigation
        dots.forEach(function(dot, index) {
            dot.addEventListener('click', function() {
                goToSlide(index);
                stopAutoPlay();
                startAutoPlay(); // Restart auto-play
            });
        });
        
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                prevSlide();
                stopAutoPlay();
                startAutoPlay();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
                stopAutoPlay();
                startAutoPlay();
            }
        });
        
        // Touch/swipe support
        let startX = 0;
        let endX = 0;
        
        slider.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });
        
        slider.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = startX - endX;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe left - next slide
                    nextSlide();
                } else {
                    // Swipe right - previous slide
                    prevSlide();
                }
                stopAutoPlay();
                startAutoPlay();
            }
        }
        
        // Resize handler
        window.addEventListener('resize', function() {
            updateSlider();
        });
    }
});
