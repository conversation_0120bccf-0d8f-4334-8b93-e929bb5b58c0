document.addEventListener('DOMContentLoaded', function() {
    const textTwoColumnBlocks = document.querySelectorAll('.goatTextTwoColumnBlock');
    
    textTwoColumnBlocks.forEach(function(block) {
        initTextTwoColumnBlock(block);
    });
    
    function initTextTwoColumnBlock(block) {
        const leftColumn = block.querySelector('.leftColumn');
        const rightColumn = block.querySelector('.rightColumn');
        const bottomCTA = block.querySelector('.bottomCTA');
        
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);
        
        // Animate columns
        if (leftColumn) {
            leftColumn.style.animationDelay = '0s';
            observer.observe(leftColumn);
        }
        
        if (rightColumn) {
            rightColumn.style.animationDelay = '0.2s';
            observer.observe(rightColumn);
        }
        
        if (bottomCTA) {
            bottomCTA.style.animationDelay = '0.4s';
            observer.observe(bottomCTA);
        }
        
        // Add hover effects to column images
        const columnImages = block.querySelectorAll('.columnImage img');
        columnImages.forEach(function(img) {
            img.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            
            img.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
        
        // Add smooth scroll for column buttons if they link to anchors
        const columnButtons = block.querySelectorAll('.columnButton a, .ctaButton a');
        columnButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                const href = this.getAttribute('href');
                if (href && href.startsWith('#')) {
                    e.preventDefault();
                    const target = document.querySelector(href);
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });
    }
});
