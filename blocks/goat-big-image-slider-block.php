<?php
$images = get_field('slider_images');
if ($images): ?>
    <section class="goatBigImageSliderBlock">
        <div class="contentWrapper">
            <div class="sliderWrapper">
                <div class="slider" data-slider data-loop-slider="true">
                    <?php foreach ($images as $img): ?>
                        <div class="slide">
                            <div class="imageWrapper">
                                <div class="innerImage">
                                    <?php
                                    $img = optimize_images_for_compressx($img);
                                    ?>
                                    <img class="lazy"
                                         data-src="<?= esc_url($img['sizes']['large']) ?>"
                                         alt="<?= esc_attr($name) ?>" />
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php if (count($images) > 1): ?>
                <div class="sliderButton arrowButton prev" data-prev><i class="icon-arrow-left"></i><i class="icon-arrow-left"></i></div>
                <div class="sliderButton arrowButton next" data-next><i class="icon-arrow-right"></i><i class="icon-arrow-right"></i></div>
                <?php endif; ?>
            </div>
        </div>
    </section>
<?php endif; ?>
