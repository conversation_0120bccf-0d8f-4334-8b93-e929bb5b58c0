<section class="goatEventsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
        <div class="sectionHeader">
            <?php if (get_field("title")) : ?>
                <h2 class="mediumTitle"><?php the_field("title"); ?></h2>
            <?php endif; ?>
        </div>
        
        <!-- <?php if (get_field("show_event_filters")) : ?>
            <div class="eventFilters">
                <button class="filterBtn active" data-filter="all">All Events</button>
                <button class="filterBtn" data-filter="upcoming">Upcoming</button>
                <button class="filterBtn" data-filter="past">Past Events</button>
            </div>
        <?php endif; ?> -->
        
        <div class="events upcoming">
            <?php 
            $events_per_page = get_field('events_per_page') ?: 6;
            $events = get_posts(array(
                'post_type' => 'event',
                'posts_per_page' => $events_per_page,
                'post_status' => 'publish',
                'meta_key' => 'event_date',
                'orderby' => 'meta_value',
                'order' => 'DESC'
            ));

            $events = array_filter($events, function($event) {
                return strtotime(get_field('event_date', $event->ID)) > time();
            });
            
            if ($events) : 
                foreach ($events as $event) : 
                    $event_id = $event->ID;
                    $event_date = get_field('event_date', $event_id);
                    $is_upcoming = $event_date && strtotime($event_date) > time();
                    $event_images = get_field('event_images', $event_id);
                    $featured_image = $event_images ? $event_images[0] : null;
            ?>
                <a href="<?php echo get_permalink($event_id); ?>" title="<?php echo get_the_title($event_id); ?>" class="event" data-category="<?php echo $is_upcoming ? 'upcoming' : 'past'; ?>" data-show-mouse>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($event_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($event_id)); ?>">
                        </div>
                    </div>
                    <div class="eventInfo">
                        <?php if (get_field('event_date', $event_id)) : ?>
                            <div class="smallTitle primary"><?php echo get_field('event_date', $event_id); ?></div>
                        <?php endif; ?>
                        <h3 class="eventTitle normalTitle"><?php echo get_the_title($event_id); ?></h3>
                    </div>
                </a>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
        <h3 class="subTitle primary"><?php echo get_field("subtitle"); ?></h3>
        <div class="events legacy">
            <?php 
            $events = get_posts(array(
                'post_type' => 'event',
                'posts_per_page' => -1,
                'post_status' => 'publish',
                'meta_key' => 'event_date',
                'orderby' => 'meta_value',
                'order' => 'DESC'
            ));
            
            $events = array_filter($events, function($event) {
                return strtotime(get_field('event_date', $event->ID)) < time();
            });

            if ($events) : 
                foreach ($events as $event) : 
                    $event_id = $event->ID;
                    $event_date = get_field('event_date', $event_id);
                    $is_upcoming = $event_date && strtotime($event_date) > time();
                    $event_images = get_field('event_images', $event_id);
                    $featured_image = $event_images ? $event_images[0] : null;
            ?>
                <a href="<?php echo get_permalink($event_id); ?>" title="<?php echo get_the_title($event_id); ?>" class="event" data-category="<?php echo $is_upcoming ? 'upcoming' : 'past'; ?>" data-show-mouse>
                    <div class="imageWrapper">
                        <div class="innerImage">
                            <img class="lazy" data-src="<?php echo get_the_post_thumbnail_url($event_id, 'medium_large'); ?>" alt="<?php echo esc_attr(get_the_title($event_id)); ?>">
                        </div>
                    </div>
                    <div class="eventInfo">
                        <?php if (get_field('event_date', $event_id)) : ?>
                            <div class="smallTitle primary"><?php echo get_field('event_date', $event_id); ?></div>
                        <?php endif; ?>
                        <h3 class="eventTitle normalTitle"><?php echo get_the_title($event_id); ?></h3>
                    </div>
                </a>
            <?php 
                endforeach;
            endif; 
            ?>
        </div>
    </div>
</section>
