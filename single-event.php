<?php get_header(); ?>

<div class="event-detail">
    <div class="container">
        <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
            
            <div class="event-header">
                <h1 class="event-title"><?php the_title(); ?></h1>
                
                <?php if (get_field('event_date')) : ?>
                    <div class="event-date">
                        <i class="icon-calendar"></i>
                        <span><?php echo get_field('event_date'); ?></span>
                    </div>
                <?php endif; ?>
                
                <?php if (get_field('event_location')) : ?>
                    <div class="event-location">
                        <i class="icon-location"></i>
                        <span><?php echo get_field('event_location'); ?></span>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (get_field('event_images')) : ?>
                <div class="event-gallery">
                    <?php 
                    $images = get_field('event_images');
                    if ($images) : ?>
                        <div class="image-slider">
                            <?php foreach ($images as $image) : ?>
                                <div class="slide">
                                    <img src="<?php echo esc_url($image['sizes']['large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="event-content">
                <?php the_content(); ?>
            </div>

            <?php if (get_field('event_description')) : ?>
                <div class="event-description">
                    <h2>Event Details</h2>
                    <p><?php echo get_field('event_description'); ?></p>
                </div>
            <?php endif; ?>

            <?php if (get_field('event_registration_link')) : ?>
                <div class="event-registration">
                    <?php 
                    $link = get_field('event_registration_link');
                    if ($link) : ?>
                        <a href="<?php echo esc_url($link['url']); ?>" class="button event-register-btn" target="<?php echo esc_attr($link['target'] ?: '_self'); ?>">
                            <span class="innerText"><?php echo esc_html($link['title']); ?></span>
                            <span class="arrows">
                                <i class="icon-arrow-right-up"></i>
                                <i class="icon-arrow-right-up"></i>
                            </span>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        <?php endwhile; endif; ?>
    </div>
</div>

<?php get_footer(); ?>
