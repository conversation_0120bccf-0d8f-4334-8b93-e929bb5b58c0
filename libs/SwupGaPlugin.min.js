!function(e, t) {
    "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e || self).SwupGaPlugin = t()
}(this, function() {
    function e() {
        return e = Object.assign ? Object.assign.bind() : function(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = arguments[t];
                for (var i in n)
                    Object.prototype.hasOwnProperty.call(n, i) && (e[i] = n[i])
            }
            return e
        }
        ,
        e.apply(this, arguments)
    }
    const t = e => String(e).split(".").map(e => String(parseInt(e || "0", 10))).concat(["0", "0"]).slice(0, 3).join(".");
    class n {
        constructor() {
            this.isSwupPlugin = !0,
            this.swup = void 0,
            this.version = void 0,
            this.requires = {},
            this.handlersToUnregister = []
        }
        mount() {}
        unmount() {
            this.handlersToUnregister.forEach(e => e()),
            this.handlersToUnregister = []
        }
        _beforeMount() {
            if (!this.name)
                throw new Error("You must define a name of plugin when creating a class.")
        }
        _afterUnmount() {}
        _checkRequirements() {
            return "object" != typeof this.requires || Object.entries(this.requires).forEach( ([e,n]) => {
                if (!function(e, n, i) {
                    const r = function(e, t) {
                        var n;
                        if ("swup" === e)
                            return null != (n = t.version) ? n : "";
                        {
                            var i;
                            const n = t.findPlugin(e);
                            return null != (i = null == n ? void 0 : n.version) ? i : ""
                        }
                    }(e, i);
                    return !!r && ( (e, n) => n.every(n => {
                        const [,i,r] = n.match(/^([\D]+)?(.*)$/) || [];
                        var o, s;
                        return ( (e, t) => {
                            const n = {
                                "": e => 0 === e,
                                ">": e => e > 0,
                                ">=": e => e >= 0,
                                "<": e => e < 0,
                                "<=": e => e <= 0
                            };
                            return (n[t] || n[""])(e)
                        }
                        )((s = r,
                        o = t(o = e),
                        s = t(s),
                        o.localeCompare(s, void 0, {
                            numeric: !0
                        })), i || ">=")
                    }
                    ))(r, n)
                }(e, n = Array.isArray(n) ? n : [n], this.swup)) {
                    const t = `${e} ${n.join(", ")}`;
                    throw new Error(`Plugin version mismatch: ${this.name} requires ${t}`)
                }
            }
            ),
            !0
        }
        on(e, t, n={}) {
            var i;
            t = !(i = t).name.startsWith("bound ") || i.hasOwnProperty("prototype") ? t.bind(this) : t;
            const r = this.swup.hooks.on(e, t, n);
            return this.handlersToUnregister.push(r),
            r
        }
        once(t, n, i={}) {
            return this.on(t, n, e({}, i, {
                once: !0
            }))
        }
        before(t, n, i={}) {
            return this.on(t, n, e({}, i, {
                before: !0
            }))
        }
        replace(t, n, i={}) {
            return this.on(t, n, e({}, i, {
                replace: !0
            }))
        }
        off(e, t) {
            return this.swup.hooks.off(e, t)
        }
    }
    return class extends n {
        constructor(e) {
            void 0 === e && (e = {}),
            super(),
            this.name = "SwupGaPlugin",
            this.requires = {
                swup: ">=4"
            },
            this.defaults = {
                gaMeasurementId: null
            },
            this.options = {
                ...this.defaults,
                ...e
            }
        }
        mount() {
            this.on("page:view", this.trackPageView)
        }
        trackPageView() {
            const e = document.title
              , t = window.location.pathname + window.location.search;
            "function" == typeof window.gtag ? (this.trackPageViewInGtag({
                title: e,
                url: t
            }),
            this.swup.log(`GA page view: ${t} (gtag.js)`)) : "function" == typeof window.ga ? (this.trackPageViewInGa({
                title: e,
                url: t
            }),
            this.swup.log(`GA page view: ${t} (analytics.js)`)) : console.warn("Neither window.gtag nor window.ga are present on the page")
        }
        trackPageViewInGtag(e) {
            let {title: t, url: n} = e;
            const {gaMeasurementId: i} = this.options;
            i ? window.gtag("config", i, {
                page_title: t,
                page_path: n
            }) : console.error("The gaMeasurementId option is required for gtag.js")
        }
        trackPageViewInGa(e) {
            let {title: t, url: n} = e;
            window.ga("set", "title", t),
            window.ga("set", "page", n),
            window.ga("send", "pageview")
        }
    }
});
