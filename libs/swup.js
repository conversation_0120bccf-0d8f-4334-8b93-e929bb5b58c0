!function(t, e) {
    "object" == typeof exports && "undefined" != typeof module ? module.exports = e() : "function" == typeof define && define.amd ? define(e) : (t || self).Swup = e()
}(this, function() {
    const t = new WeakMap;
    function e(e, n, r, o) {
        if (!e && !t.has(n))
            return !1;
        const i = t.get(n) ?? new WeakMap;
        t.set(n, i);
        const s = i.get(r) ?? new Set;
        i.set(r, s);
        const a = s.has(o);
        return e ? s.add(o) : s.delete(o),
        a && e
    }
    const n = (t, e) => String(t).toLowerCase().replace(/[\s/_.]+/g, "-").replace(/[^\w-]+/g, "").replace(/--+/g, "-").replace(/^-+|-+$/g, "") || e || ""
      , r = function(t) {
        let {hash: e} = void 0 === t ? {} : t;
        return location.pathname + location.search + (e ? location.hash : "")
    }
      , o = function(t, e) {
        void 0 === e && (e = {});
        const n = {
            url: t = t || r({
                hash: !0
            }),
            random: Math.random(),
            source: "swup",
            ...e
        };
        history.pushState(n, "", t)
    }
      , i = function(t, e) {
        void 0 === t && (t = null),
        void 0 === e && (e = {}),
        t = t || r({
            hash: !0
        });
        const n = {
            ...history.state || {},
            url: t,
            random: Math.random(),
            source: "swup",
            ...e
        };
        history.replaceState(n, "", t)
    }
      , s = (t, n, r, o) => {
        const i = new AbortController;
        return function(t, n, r, o={}) {
            const {signal: i, base: s=document} = o;
            if (i?.aborted)
                return;
            const {once: a, ...c} = o
              , l = s instanceof Document ? s.documentElement : s
              , h = Boolean("object" == typeof o ? o.capture : o)
              , u = o => {
                const i = function(t, e) {
                    let n = t.target;
                    if (n instanceof Text && (n = n.parentElement),
                    n instanceof Element && t.currentTarget instanceof Element) {
                        const r = n.closest(e);
                        if (r && t.currentTarget.contains(r))
                            return r
                    }
                }(o, t);
                if (i) {
                    const t = Object.assign(o, {
                        delegateTarget: i
                    });
                    r.call(l, t),
                    a && (l.removeEventListener(n, u, c),
                    e(!1, l, r, d))
                }
            }
              , d = JSON.stringify({
                selector: t,
                type: n,
                capture: h
            });
            e(!0, l, r, d) || l.addEventListener(n, u, c),
            i?.addEventListener("abort", () => {
                e(!1, l, r, d)
            }
            )
        }(t, n, r, o = {
            ...o,
            signal: i.signal
        }),
        {
            destroy: () => i.abort()
        }
    }
    ;
    class a extends URL {
        constructor(t, e) {
            void 0 === e && (e = document.baseURI),
            super(t.toString(), e)
        }
        get url() {
            return this.pathname + this.search
        }
        static fromElement(t) {
            const e = t.getAttribute("href") || t.getAttribute("xlink:href") || "";
            return new a(e)
        }
        static fromUrl(t) {
            return new a(t)
        }
    }
    const c = function(t, e) {
        void 0 === e && (e = {});
        try {
            const r = this;
            function n(n) {
                const {status: o, url: i} = h;
                return Promise.resolve(h.text()).then(function(n) {
                    if (500 === o)
                        throw r.hooks.call("fetch:error", {
                            status: o,
                            response: h,
                            url: i
                        }),
                        new l(`Server error: ${i}`,{
                            status: o,
                            url: i
                        });
                    if (!n)
                        throw new l(`Empty response: ${i}`,{
                            status: o,
                            url: i
                        });
                    const {url: s} = a.fromUrl(i)
                      , c = {
                        url: s,
                        html: n
                    };
                    return !r.visit.cache.write || e.method && "GET" !== e.method || t !== s || r.cache.set(c.url, c),
                    c
                })
            }
            t = a.fromUrl(t).url;
            const o = {
                ...r.options.requestHeaders,
                ...e.headers
            }
              , i = e.timeout ?? r.options.timeout
              , s = new AbortController
              , {signal: c} = s;
            e = {
                ...e,
                headers: o,
                signal: c
            };
            let h, u = !1, d = null;
            i && i > 0 && (d = setTimeout( () => {
                u = !0,
                s.abort("timeout")
            }
            , i));
            const m = function(n, o) {
                try {
                    var i = Promise.resolve(r.hooks.call("fetch:request", {
                        url: t,
                        options: e
                    }, (t, e) => {
                        let {url: n, options: r} = e;
                        return fetch(n, r)
                    }
                    )).then(function(t) {
                        h = t,
                        d && clearTimeout(d)
                    })
                } catch (t) {
                    return o(t)
                }
                return i && i.then ? i.then(void 0, o) : i
            }(0, function(e) {
                if (u)
                    throw r.hooks.call("fetch:timeout", {
                        url: t
                    }),
                    new l(`Request timed out: ${t}`,{
                        url: t,
                        timedOut: u
                    });
                if ("AbortError" === e?.name || c.aborted)
                    throw new l(`Request aborted: ${t}`,{
                        url: t,
                        aborted: !0
                    });
                throw e
            });
            return Promise.resolve(m && m.then ? m.then(n) : n())
        } catch (f) {
            return Promise.reject(f)
        }
    };
    class l extends Error {
        constructor(t, e) {
            super(t),
            this.url = void 0,
            this.status = void 0,
            this.aborted = void 0,
            this.timedOut = void 0,
            this.name = "FetchError",
            this.url = e.url,
            this.status = e.status,
            this.aborted = e.aborted || !1,
            this.timedOut = e.timedOut || !1
        }
    }
    class h {
        constructor(t) {
            this.swup = void 0,
            this.pages = new Map,
            this.swup = t
        }
        get size() {
            return this.pages.size
        }
        get all() {
            const t = new Map;
            return this.pages.forEach( (e, n) => {
                t.set(n, {
                    ...e
                })
            }
            ),
            t
        }
        has(t) {
            return this.pages.has(this.resolve(t))
        }
        get(t) {
            const e = this.pages.get(this.resolve(t));
            return e ? {
                ...e
            } : e
        }
        set(t, e) {
            t = this.resolve(t),
            e = {
                ...e,
                url: t
            },
            this.pages.set(t, e),
            this.swup.hooks.callSync("cache:set", {
                page: e
            })
        }
        update(t, e) {
            t = this.resolve(t);
            const n = {
                ...this.get(t),
                ...e,
                url: t
            };
            this.pages.set(t, n)
        }
        delete(t) {
            this.pages.delete(this.resolve(t))
        }
        clear() {
            this.pages.clear(),
            this.swup.hooks.callSync("cache:clear", void 0)
        }
        prune(t) {
            this.pages.forEach( (e, n) => {
                t(n, e) && this.delete(n)
            }
            )
        }
        resolve(t) {
            const {url: e} = a.fromUrl(t);
            return this.swup.resolveUrl(e)
        }
    }
    const u = function(t, e) {
        return void 0 === e && (e = document),
        e.querySelector(t)
    }
      , d = function(t, e) {
        return void 0 === e && (e = document),
        Array.from(e.querySelectorAll(t))
    }
      , m = () => new Promise(t => {
        requestAnimationFrame( () => {
            requestAnimationFrame( () => {
                t()
            }
            )
        }
        )
    }
    );
    function f(t) {
        return !!t && ("object" == typeof t || "function" == typeof t) && "function" == typeof t.then
    }
    const p = t => window.CSS && window.CSS.escape ? CSS.escape(t) : t
      , v = t => 1e3 * Number(t.slice(0, -1).replace(",", "."));
    class g {
        constructor(t) {
            this.swup = void 0,
            this.swupClasses = ["to-", "is-changing", "is-rendering", "is-popstate", "is-animating"],
            this.swup = t
        }
        get selectors() {
            const {scope: t} = this.swup.visit.animation;
            return "containers" === t ? this.swup.visit.containers : "html" === t ? ["html"] : Array.isArray(t) ? t : []
        }
        get selector() {
            return this.selectors.join(",")
        }
        get targets() {
            return this.selector.trim() ? d(this.selector) : []
        }
        add() {
            this.targets.forEach(t => t.classList.add(...[].slice.call(arguments)))
        }
        remove() {
            this.targets.forEach(t => t.classList.remove(...[].slice.call(arguments)))
        }
        clear() {
            this.targets.forEach(t => {
                const e = t.className.split(" ").filter(t => this.isSwupClass(t));
                t.classList.remove(...e)
            }
            )
        }
        isSwupClass(t) {
            return this.swupClasses.some(e => t.startsWith(e))
        }
    }
    function w(t) {
        let {to: e, from: n=this.currentPageUrl, hash: r, el: o, event: i} = t;
        return {
            id: Math.random(),
            from: {
                url: n
            },
            to: {
                url: e,
                hash: r
            },
            containers: this.options.containers,
            animation: {
                animate: !0,
                wait: !1,
                name: void 0,
                scope: this.options.animationScope,
                selector: this.options.animationSelector
            },
            trigger: {
                el: o,
                event: i
            },
            cache: {
                read: this.options.cache,
                write: this.options.cache
            },
            history: {
                action: "push",
                popstate: !1,
                direction: void 0
            },
            scroll: {
                reset: !0,
                target: void 0
            }
        }
    }
    const y = "undefined" != typeof Symbol ? Symbol.iterator || (Symbol.iterator = Symbol("Symbol.iterator")) : "@@iterator";
    function P(t, e, n) {
        if (!t.s) {
            if (n instanceof k) {
                if (!n.s)
                    return void (n.o = P.bind(null, t, e));
                1 & e && (e = n.s),
                n = n.v
            }
            if (n && n.then)
                return void n.then(P.bind(null, t, e), P.bind(null, t, 2));
            t.s = e,
            t.v = n;
            const r = t.o;
            r && r(t)
        }
    }
    const k = /*#__PURE__*/
    function() {
        function t() {}
        return t.prototype.then = function(e, n) {
            const r = new t
              , o = this.s;
            if (o) {
                const t = 1 & o ? e : n;
                if (t) {
                    try {
                        P(r, 1, t(this.v))
                    } catch (t) {
                        P(r, 2, t)
                    }
                    return r
                }
                return this
            }
            return this.o = function(t) {
                try {
                    const o = t.v;
                    1 & t.s ? P(r, 1, e ? e(o) : o) : n ? P(r, 1, n(o)) : P(r, 2, o)
                } catch (t) {
                    P(r, 2, t)
                }
            }
            ,
            r
        }
        ,
        t
    }();
    function b(t) {
        return t instanceof k && 1 & t.s
    }
    class S {
        constructor(t) {
            this.swup = void 0,
            this.registry = new Map,
            this.hooks = ["animation:out:start", "animation:out:await", "animation:out:end", "animation:in:start", "animation:in:await", "animation:in:end", "animation:skip", "cache:clear", "cache:set", "content:replace", "content:scroll", "enable", "disable", "fetch:request", "fetch:error", "fetch:timeout", "history:popstate", "link:click", "link:self", "link:anchor", "link:newtab", "page:load", "page:view", "scroll:top", "scroll:anchor", "visit:start", "visit:transition", "visit:end"],
            this.swup = t,
            this.init()
        }
        init() {
            this.hooks.forEach(t => this.create(t))
        }
        create(t) {
            this.registry.has(t) || this.registry.set(t, new Map)
        }
        exists(t) {
            return this.registry.has(t)
        }
        get(t) {
            const e = this.registry.get(t);
            if (e)
                return e;
            console.error(`Unknown hook '${t}'`)
        }
        clear() {
            this.registry.forEach(t => t.clear())
        }
        on(t, e, n) {
            void 0 === n && (n = {});
            const r = this.get(t);
            if (!r)
                return console.warn(`Hook '${t}' not found.`),
                () => {}
                ;
            const o = r.size + 1
              , i = {
                ...n,
                id: o,
                hook: t,
                handler: e
            };
            return r.set(e, i),
            () => this.off(t, e)
        }
        before(t, e, n) {
            return void 0 === n && (n = {}),
            this.on(t, e, {
                ...n,
                before: !0
            })
        }
        replace(t, e, n) {
            return void 0 === n && (n = {}),
            this.on(t, e, {
                ...n,
                replace: !0
            })
        }
        once(t, e, n) {
            return void 0 === n && (n = {}),
            this.on(t, e, {
                ...n,
                once: !0
            })
        }
        off(t, e) {
            const n = this.get(t);
            n && e ? n.delete(e) || console.warn(`Handler for hook '${t}' not found.`) : n && n.clear()
        }
        call(t, e, n) {
            try {
                const r = this
                  , {before: o, handler: i, after: s} = r.getHandlers(t, n);
                return Promise.resolve(r.run(o, e)).then(function() {
                    return Promise.resolve(r.run(i, e)).then(function(n) {
                        let[o] = n;
                        return Promise.resolve(r.run(s, e)).then(function() {
                            return r.dispatchDomEvent(t, e),
                            o
                        })
                    })
                })
            } catch (t) {
                return Promise.reject(t)
            }
        }
        callSync(t, e, n) {
            const {before: r, handler: o, after: i} = this.getHandlers(t, n);
            this.runSync(r, e);
            const [s] = this.runSync(o, e);
            return this.runSync(i, e),
            this.dispatchDomEvent(t, e),
            s
        }
        run(t, e) {
            try {
                const n = this
                  , r = []
                  , o = function(t, e, n) {
                    if ("function" == typeof t[y]) {
                        var r, o, i, s = t[y]();
                        if (function t(n) {
                            try {
                                for (; !(r = s.next()).done; )
                                    if ((n = e(r.value)) && n.then) {
                                        if (!b(n))
                                            return void n.then(t, i || (i = P.bind(null, o = new k, 2)));
                                        n = n.v
                                    }
                                o ? P(o, 1, n) : o = n
                            } catch (t) {
                                P(o || (o = new k), 2, t)
                            }
                        }(),
                        s.return) {
                            var a = function(t) {
                                try {
                                    r.done || s.return()
                                } catch (t) {}
                                return t
                            };
                            if (o && o.then)
                                return o.then(a, function(t) {
                                    throw a(t)
                                });
                            a()
                        }
                        return o
                    }
                    if (!("length"in t))
                        throw new TypeError("Object is not iterable");
                    for (var c = [], l = 0; l < t.length; l++)
                        c.push(t[l]);
                    return function(t, e, n) {
                        var r, o, i = -1;
                        return function n(s) {
                            try {
                                for (; ++i < t.length; )
                                    if ((s = e(i)) && s.then) {
                                        if (!b(s))
                                            return void s.then(n, o || (o = P.bind(null, r = new k, 2)));
                                        s = s.v
                                    }
                                r ? P(r, 1, s) : r = s
                            } catch (t) {
                                P(r || (r = new k), 2, t)
                            }
                        }(),
                        r
                    }(c, function(t) {
                        return e(c[t])
                    })
                }(t, function(t) {
                    let {hook: o, handler: i, defaultHandler: s, once: a} = t;
                    return Promise.resolve(function(t, e) {
                        return void 0 === e && (e = []),
                        new Promise( (n, r) => {
                            const o = t(...e);
                            f(o) ? o.then(n, r) : n(o)
                        }
                        )
                    }(i, [n.swup.visit, e, s])).then(function(t) {
                        r.push(t),
                        a && n.off(o, i)
                    })
                });
                return Promise.resolve(o && o.then ? o.then(function() {
                    return r
                }) : r)
            } catch (t) {
                return Promise.reject(t)
            }
        }
        runSync(t, e) {
            const n = [];
            for (const {hook: r, handler: o, defaultHandler: i, once: s} of t) {
                const t = o(this.swup.visit, e, i);
                n.push(t),
                f(t) && console.warn(`Promise returned from handler for synchronous hook '${r}'.Swup will not wait for it to resolve.`),
                s && this.off(r, o)
            }
            return n
        }
        getHandlers(t, e) {
            const n = this.get(t);
            if (!n)
                return {
                    found: !1,
                    before: [],
                    handler: [],
                    after: [],
                    replaced: !1
                };
            const r = Array.from(n.values())
              , o = this.sortRegistrations
              , i = r.filter(t => {
                let {before: e, replace: n} = t;
                return e && !n
            }
            ).sort(o)
              , s = r.filter(t => {
                let {replace: e} = t;
                return e
            }
            ).filter(t => !0).sort(o)
              , a = r.filter(t => {
                let {before: e, replace: n} = t;
                return !e && !n
            }
            ).sort(o)
              , c = s.length > 0;
            let l = [];
            if (e && (l = [{
                id: 0,
                hook: t,
                handler: e
            }],
            c)) {
                const n = s.length - 1
                  , r = t => {
                    const n = s[t - 1];
                    return n ? (e, o) => n.handler(e, o, r(t - 1)) : e
                }
                ;
                l = [{
                    id: 0,
                    hook: t,
                    handler: s[n].handler,
                    defaultHandler: r(n)
                }]
            }
            return {
                found: !0,
                before: i,
                handler: l,
                after: a,
                replaced: c
            }
        }
        sortRegistrations(t, e) {
            return (t.priority ?? 0) - (e.priority ?? 0) || t.id - e.id || 0
        }
        dispatchDomEvent(t, e) {
            document.dispatchEvent(new CustomEvent(`swup:${t}`,{
                detail: {
                    hook: t,
                    args: e,
                    visit: this.swup.visit
                }
            }))
        }
    }
    const E = t => {
        if (t && "#" === t.charAt(0) && (t = t.substring(1)),
        !t)
            return null;
        const e = decodeURIComponent(t);
        let n = document.getElementById(t) || document.getElementById(e) || u(`a[name='${p(t)}']`) || u(`a[name='${p(e)}']`);
        return n || "top" !== t || (n = document.body),
        n
    }
      , U = function(t) {
        let {elements: e, selector: n} = t;
        try {
            if (!1 === n && !e)
                return Promise.resolve();
            let t = [];
            if (e)
                t = Array.from(e);
            else if (n && (t = d(n, document.body),
            !t.length))
                return console.warn(`[swup] No elements found matching animationSelector \`${n}\``),
                Promise.resolve();
            const r = t.map(t => function(t) {
                const {type: e, timeout: n, propCount: r} = function(t, e) {
                    const n = window.getComputedStyle(t)
                      , r = $(n, `${C}Delay`)
                      , o = $(n, `${C}Duration`)
                      , i = H(r, o)
                      , s = $(n, `${x}Delay`)
                      , a = $(n, `${x}Duration`)
                      , c = H(s, a);
                    let l = null
                      , h = 0
                      , u = 0;
                    return e === C ? i > 0 && (l = C,
                    h = i,
                    u = o.length) : e === x ? c > 0 && (l = x,
                    h = c,
                    u = a.length) : (h = Math.max(i, c),
                    l = h > 0 ? i > c ? C : x : null,
                    u = l ? l === C ? o.length : a.length : 0),
                    {
                        type: l,
                        timeout: h,
                        propCount: u
                    }
                }(t);
                return !(!e || !n) && new Promise(o => {
                    const i = `${e}end`
                      , s = performance.now();
                    let a = 0;
                    const c = () => {
                        t.removeEventListener(i, l),
                        o()
                    }
                      , l = e => {
                        if (e.target === t) {
                            if (!function(t) {
                                return [`${C}end`, `${x}end`].includes(t.type)
                            }(e))
                                throw new Error("Not a transition or animation event.");
                            (performance.now() - s) / 1e3 < e.elapsedTime || ++a >= r && c()
                        }
                    }
                    ;
                    setTimeout( () => {
                        a < r && c()
                    }
                    , n + 1),
                    t.addEventListener(i, l)
                }
                )
            }(t));
            return r.filter(Boolean).length > 0 ? Promise.resolve(Promise.all(r)).then(function() {}) : (n && console.warn(`[swup] No CSS animation duration defined on elements matching \`${n}\``),
            Promise.resolve())
        } catch (t) {
            return Promise.reject(t)
        }
    }
      , C = "transition"
      , x = "animation";
    function $(t, e) {
        return (t[e] || "").split(", ")
    }
    function H(t, e) {
        for (; t.length < e.length; )
            t = t.concat(t);
        return Math.max(...e.map( (e, n) => v(e) + v(t[n])))
    }
    const A = function(t) {
        void 0 === t && (t = {});
        try {
            const e = this;
            e.navigating = !0;
            const n = e.visit
              , {el: s} = n.trigger;
            t.referrer = t.referrer || e.currentPageUrl,
            !1 === t.animate && (n.animation.animate = !1),
            n.animation.animate || e.classes.clear();
            const a = t.history || s?.getAttribute("data-swup-history") || void 0;
            a && ["push", "replace"].includes(a) && (n.history.action = a);
            const c = t.animation || s?.getAttribute("data-swup-animation") || void 0;
            return c && (n.animation.name = c),
            "object" == typeof t.cache ? (n.cache.read = t.cache.read ?? n.cache.read,
            n.cache.write = t.cache.write ?? n.cache.write) : void 0 !== t.cache && (n.cache = {
                read: !!t.cache,
                write: !!t.cache
            }),
            delete t.cache,
            Promise.resolve(function(s, a) {
                try {
                    var c = Promise.resolve(e.hooks.call("visit:start", void 0)).then(function() {
                        function s() {
                            return Promise.resolve(e.hooks.call("visit:transition", void 0, function(t) {
                                try {
                                    const n = e.animatePageOut();
                                    return Promise.resolve(Promise.all([a, n])).then(function(n) {
                                        let[r] = n;
                                        return t.id === e.visit.id && Promise.resolve(e.renderPage(r)).then(function() {
                                            return Promise.resolve(e.animatePageIn()).then(function() {
                                                return !0
                                            })
                                        })
                                    })
                                } catch (t) {
                                    return Promise.reject(t)
                                }
                            })).then(function() {
                                return Promise.resolve(e.hooks.call("visit:end", void 0, () => e.classes.clear())).then(function() {
                                    e.navigating = !1
                                })
                            })
                        }
                        const a = e.hooks.call("page:load", {
                            options: t
                        }, function(t, n) {
                            try {
                                function r(t) {
                                    return n.page = t,
                                    n.cache = !!o,
                                    n.page
                                }
                                let o;
                                return t.cache.read && (o = e.cache.get(t.to.url)),
                                Promise.resolve(o ? r(o) : Promise.resolve(e.fetchPage(t.to.url, n.options)).then(r))
                            } catch (i) {
                                return Promise.reject(i)
                            }
                        });
                        if (!n.history.popstate) {
                            const t = n.to.url + n.to.hash;
                            "replace" === n.history.action || n.to.url === e.currentPageUrl ? i(t) : (e.currentHistoryIndex++,
                            o(t, {
                                index: e.currentHistoryIndex
                            }))
                        }
                        e.currentPageUrl = r();
                        const c = function() {
                            if (n.animation.wait)
                                return Promise.resolve(a).then(function(t) {
                                    let {html: e} = t;
                                    n.to.html = e
                                })
                        }();
                        return c && c.then ? c.then(s) : s()
                    })
                } catch (t) {
                    return a(t)
                }
                return c && c.then ? c.then(void 0, a) : c
            }(0, function(t) {
                t && !t?.aborted && (console.error(t),
                e.options.skipPopStateHandling = () => (window.location.href = n.to.url + n.to.hash,
                !0),
                window.history.go(-1))
            }))
        } catch (t) {
            return Promise.reject(t)
        }
    };
    function j(t, e, n) {
        if (void 0 === e && (e = {}),
        void 0 === n && (n = {}),
        "string" != typeof t)
            throw new Error("swup.navigate() requires a URL parameter");
        if (this.shouldIgnoreVisit(t, {
            el: n.el,
            event: n.event
        }))
            return void (window.location.href = t);
        const {url: r, hash: o} = a.fromUrl(t);
        this.visit = this.createVisit({
            ...n,
            to: r,
            hash: o
        }),
        this.performNavigation(e)
    }
    const T = function() {
        try {
            let e;
            const r = this;
            function t(t) {
                return e ? t : Promise.resolve(r.hooks.call("animation:out:start", void 0, t => {
                    r.classes.add("is-changing", "is-leaving", "is-animating"),
                    t.history.popstate && r.classes.add("is-popstate"),
                    t.animation.name && r.classes.add(`to-${n(t.animation.name)}`)
                }
                )).then(function() {
                    return Promise.resolve(r.hooks.call("animation:out:await", {
                        skip: !1
                    }, function(t, e) {
                        let {skip: n} = e;
                        try {
                            return n ? Promise.resolve() : Promise.resolve(r.awaitAnimations({
                                selector: t.animation.selector
                            })).then(function() {})
                        } catch (t) {
                            return Promise.reject(t)
                        }
                    })).then(function() {
                        return Promise.resolve(r.hooks.call("animation:out:end", void 0)).then(function() {})
                    })
                })
            }
            const o = function() {
                if (!r.visit.animation.animate)
                    return Promise.resolve(r.hooks.call("animation:skip", void 0)).then(function() {
                        e = 1
                    })
            }();
            return Promise.resolve(o && o.then ? o.then(t) : t(o))
        } catch (i) {
            return Promise.reject(i)
        }
    }
      , q = function(t, e) {
        let {html: n} = t
          , {containers: r} = void 0 === e ? this.options : e;
        const o = (new DOMParser).parseFromString(n, "text/html")
          , i = o.querySelector("title")?.innerText || "";
        document.title = i;
        const s = d('[data-swup-persist]:not([data-swup-persist=""])')
          , a = r.map(t => {
            const e = document.querySelector(t)
              , n = o.querySelector(t);
            return e && n ? (e.replaceWith(n),
            !0) : (e || console.warn(`[swup] Container missing in current document: ${t}`),
            n || console.warn(`[swup] Container missing in incoming document: ${t}`),
            !1)
        }
        ).filter(Boolean);
        return s.forEach(t => {
            const e = t.getAttribute("data-swup-persist")
              , n = u(`[data-swup-persist="${e}"]`);
            n && n !== t && n.replaceWith(t)
        }
        ),
        a.length === r.length
    }
      , L = function() {
        const t = {
            behavior: "auto"
        }
          , {target: e, reset: n} = this.visit.scroll
          , r = e ?? this.visit.to.hash;
        let o = !1;
        return r && (o = this.hooks.callSync("scroll:anchor", {
            hash: r,
            options: t
        }, (t, e) => {
            let {hash: n, options: r} = e;
            const o = this.getAnchorElement(n);
            return o && o.scrollIntoView(r),
            !!o
        }
        )),
        n && !o && (o = this.hooks.callSync("scroll:top", {
            options: t
        }, (t, e) => {
            let {options: n} = e;
            return window.scrollTo({
                top: 0,
                left: 0,
                ...n
            }),
            !0
        }
        )),
        o
    }
      , I = function() {
        try {
            const t = this;
            if (!t.visit.animation.animate)
                return Promise.resolve();
            const e = t.hooks.call("animation:in:await", {
                skip: !1
            }, function(e, n) {
                let {skip: r} = n;
                try {
                    return r ? Promise.resolve() : Promise.resolve(t.awaitAnimations({
                        selector: e.animation.selector
                    })).then(function() {})
                } catch (t) {
                    return Promise.reject(t)
                }
            });
            return Promise.resolve(m()).then(function() {
                return Promise.resolve(t.hooks.call("animation:in:start", void 0, () => {
                    t.classes.remove("is-animating")
                }
                )).then(function() {
                    return Promise.resolve(e).then(function() {
                        return Promise.resolve(t.hooks.call("animation:in:end", void 0)).then(function() {})
                    })
                })
            })
        } catch (t) {
            return Promise.reject(t)
        }
    }
      , R = function(t) {
        try {
            const e = this
              , {url: o, html: s} = t;
            return e.classes.remove("is-leaving"),
            e.isSameResolvedUrl(r(), o) || (i(o),
            e.currentPageUrl = r(),
            e.visit.to.url = e.currentPageUrl),
            e.visit.animation.animate && e.classes.add("is-rendering"),
            e.visit.to.html = s,
            Promise.resolve(e.hooks.call("content:replace", {
                page: t
            }, (t, r) => {
                let {page: o} = r;
                if (!e.replaceContent(o, {
                    containers: t.containers
                }))
                    throw new Error("[swup] Container mismatch, aborting");
                t.animation.animate && (e.classes.add("is-animating", "is-changing", "is-rendering"),
                t.animation.name && e.classes.add(`to-${n(t.animation.name)}`))
            }
            )).then(function() {
                return Promise.resolve(e.hooks.call("content:scroll", void 0, () => e.scrollToContent())).then(function() {
                    return Promise.resolve(e.hooks.call("page:view", {
                        url: e.currentPageUrl,
                        title: document.title
                    })).then(function() {})
                })
            })
        } catch (t) {
            return Promise.reject(t)
        }
    }
      , N = function(t) {
        var e;
        if (e = t,
        Boolean(e?.isSwupPlugin)) {
            if (t.swup = this,
            !t._checkRequirements || t._checkRequirements())
                return t._beforeMount && t._beforeMount(),
                t.mount(),
                this.plugins.push(t),
                this.plugins
        } else
            console.error("Not a swup plugin instance", t)
    };
    function D(t) {
        const e = this.findPlugin(t);
        if (e)
            return e.unmount(),
            e._afterUnmount && e._afterUnmount(),
            this.plugins = this.plugins.filter(t => t !== e),
            this.plugins;
        console.error("No such plugin", e)
    }
    function M(t) {
        return this.plugins.find(e => e === t || e.name === t || e.name === `Swup${String(t)}`)
    }
    function O(t) {
        if ("function" != typeof this.options.resolveUrl)
            return console.warn("[swup] options.resolveUrl expects a callback function."),
            t;
        const e = this.options.resolveUrl(t);
        return e && "string" == typeof e ? e.startsWith("//") || e.startsWith("http") ? (console.warn("[swup] options.resolveUrl needs to return a relative url"),
        t) : e : (console.warn("[swup] options.resolveUrl needs to return a url"),
        t)
    }
    function W(t, e) {
        return this.resolveUrl(t) === this.resolveUrl(e)
    }
    const V = {
        animateHistoryBrowsing: !1,
        animationSelector: '[class*="transition-"]',
        animationScope: "html",
        cache: !0,
        containers: ["#swup"],
        ignoreVisit: function(t, e) {
            let {el: n} = void 0 === e ? {} : e;
            return !!n?.closest("[data-no-swup]")
        },
        linkSelector: "a[href]",
        linkToSelf: "scroll",
        plugins: [],
        resolveUrl: t => t,
        requestHeaders: {
            "X-Requested-With": "swup",
            Accept: "text/html, application/xhtml+xml"
        },
        skipPopStateHandling: t => "swup" !== t.state?.source,
        timeout: 0
    };
    return class {
        constructor(t) {
            void 0 === t && (t = {}),
            this.version = "4.4.2",
            this.options = void 0,
            this.defaults = V,
            this.plugins = [],
            this.visit = void 0,
            this.cache = void 0,
            this.hooks = void 0,
            this.classes = void 0,
            this.currentPageUrl = r(),
            this.currentHistoryIndex = void 0,
            this.clickDelegate = void 0,
            this.navigating = !1,
            this.use = N,
            this.unuse = D,
            this.findPlugin = M,
            this.log = () => {}
            ,
            this.navigate = j,
            this.performNavigation = A,
            this.createVisit = w,
            this.delegateEvent = s,
            this.fetchPage = c,
            this.awaitAnimations = U,
            this.renderPage = R,
            this.replaceContent = q,
            this.animatePageIn = I,
            this.animatePageOut = T,
            this.scrollToContent = L,
            this.getAnchorElement = E,
            this.getCurrentUrl = r,
            this.resolveUrl = O,
            this.isSameResolvedUrl = W,
            this.options = {
                ...this.defaults,
                ...t
            },
            this.handleLinkClick = this.handleLinkClick.bind(this),
            this.handlePopState = this.handlePopState.bind(this),
            this.cache = new h(this),
            this.classes = new g(this),
            this.hooks = new S(this),
            this.visit = this.createVisit({
                to: ""
            }),
            this.currentHistoryIndex = history.state?.index ?? 1,
            this.checkRequirements() && this.enable()
        }
        checkRequirements() {
            return "undefined" != typeof Promise || (console.warn("Promise is not supported"),
            !1)
        }
        enable() {
            try {
                const t = this
                  , {linkSelector: e} = t.options;
                return t.clickDelegate = t.delegateEvent(e, "click", t.handleLinkClick),
                window.addEventListener("popstate", t.handlePopState),
                t.options.animateHistoryBrowsing && (window.history.scrollRestoration = "manual"),
                t.options.plugins.forEach(e => t.use(e)),
                "swup" !== history.state?.source && i(null, {
                    index: t.currentHistoryIndex
                }),
                Promise.resolve(m()).then(function() {
                    return Promise.resolve(t.hooks.call("enable", void 0, () => {
                        document.documentElement.classList.add("swup-enabled")
                    }
                    )).then(function() {})
                })
            } catch (t) {
                return Promise.reject(t)
            }
        }
        destroy() {
            try {
                const t = this;
                return t.clickDelegate.destroy(),
                window.removeEventListener("popstate", t.handlePopState),
                t.cache.clear(),
                t.options.plugins.forEach(e => t.unuse(e)),
                Promise.resolve(t.hooks.call("disable", void 0, () => {
                    document.documentElement.classList.remove("swup-enabled")
                }
                )).then(function() {
                    t.hooks.clear()
                })
            } catch (t) {
                return Promise.reject(t)
            }
        }
        shouldIgnoreVisit(t, e) {
            let {el: n, event: r} = void 0 === e ? {} : e;
            const {origin: o, url: i, hash: s} = a.fromUrl(t);
            return o !== window.location.origin || !(!n || !this.triggerWillOpenNewWindow(n)) || !!this.options.ignoreVisit(i + s, {
                el: n,
                event: r
            })
        }
        handleLinkClick(t) {
            const e = t.delegateTarget
              , {href: n, url: r, hash: o} = a.fromElement(e);
            this.shouldIgnoreVisit(n, {
                el: e,
                event: t
            }) || (this.navigating && r === this.visit.to.url ? t.preventDefault() : (this.visit = this.createVisit({
                to: r,
                hash: o,
                el: e,
                event: t
            }),
            t.metaKey || t.ctrlKey || t.shiftKey || t.altKey ? this.hooks.call("link:newtab", {
                href: n
            }) : 0 === t.button && this.hooks.callSync("link:click", {
                el: e,
                event: t
            }, () => {
                const e = this.visit.from.url ?? "";
                t.preventDefault(),
                r && r !== e ? this.isSameResolvedUrl(r, e) || this.performNavigation() : o ? this.hooks.callSync("link:anchor", {
                    hash: o
                }, () => {
                    i(r + o),
                    this.scrollToContent()
                }
                ) : this.hooks.callSync("link:self", void 0, () => "navigate" === this.options.linkToSelf ? this.performNavigation() : (i(r),
                this.scrollToContent()))
            }
            )))
        }
        handlePopState(t) {
            const e = t.state?.url ?? location.href;
            if (this.options.skipPopStateHandling(t))
                return;
            if (this.isSameResolvedUrl(r(), this.currentPageUrl))
                return;
            const {url: n, hash: o} = a.fromUrl(e);
            this.visit = this.createVisit({
                to: n,
                hash: o,
                event: t
            }),
            this.visit.history.popstate = !0;
            const i = t.state?.index ?? 0;
            i && i !== this.currentHistoryIndex && (this.visit.history.direction = i - this.currentHistoryIndex > 0 ? "forwards" : "backwards",
            this.currentHistoryIndex = i),
            this.visit.animation.animate = !1,
            this.visit.scroll.reset = !1,
            this.visit.scroll.target = !1,
            this.options.animateHistoryBrowsing && (this.visit.animation.animate = !0,
            this.visit.scroll.reset = !0),
            this.hooks.callSync("history:popstate", {
                event: t
            }, () => {
                this.performNavigation()
            }
            )
        }
        triggerWillOpenNewWindow(t) {
            return !!t.matches('[download], [target="_blank"]')
        }
    }
});
