!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t||self).SwupScriptsPlugin=e()}(this,function(){function t(){return t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},t.apply(this,arguments)}const e=t=>String(t).split(".").map(t=>String(parseInt(t||"0",10))).concat(["0","0"]).slice(0,3).join(".");class n{constructor(){this.isSwupPlugin=!0,this.swup=void 0,this.version=void 0,this.requires={},this.handlersToUnregister=[]}mount(){}unmount(){this.handlersToUnregister.forEach(t=>t()),this.handlersToUnregister=[]}_beforeMount(){if(!this.name)throw new Error("You must define a name of plugin when creating a class.")}_afterUnmount(){}_checkRequirements(){return"object"!=typeof this.requires||Object.entries(this.requires).forEach(([t,n])=>{if(!function(t,n,r){const o=function(t,e){var n;if("swup"===t)return null!=(n=e.version)?n:"";{var r;const n=e.findPlugin(t);return null!=(r=null==n?void 0:n.version)?r:""}}(t,r);return!!o&&((t,n)=>n.every(n=>{const[,r,o]=n.match(/^([\D]+)?(.*)$/)||[];var s,i;return((t,e)=>{const n={"":t=>0===t,">":t=>t>0,">=":t=>t>=0,"<":t=>t<0,"<=":t=>t<=0};return(n[e]||n[""])(t)})((i=o,s=e(s=t),i=e(i),s.localeCompare(i,void 0,{numeric:!0})),r||">=")}))(o,n)}(t,n=Array.isArray(n)?n:[n],this.swup)){const e=`${t} ${n.join(", ")}`;throw new Error(`Plugin version mismatch: ${this.name} requires ${e}`)}}),!0}on(t,e,n={}){var r;e=!(r=e).name.startsWith("bound ")||r.hasOwnProperty("prototype")?e.bind(this):e;const o=this.swup.hooks.on(t,e,n);return this.handlersToUnregister.push(o),o}once(e,n,r={}){return this.on(e,n,t({},r,{once:!0}))}before(e,n,r={}){return this.on(e,n,t({},r,{before:!0}))}replace(e,n,r={}){return this.on(e,n,t({},r,{replace:!0}))}off(t,e){return this.swup.hooks.off(t,e)}}return class extends n{constructor(t){void 0===t&&(t={}),super(),this.name="SwupScriptsPlugin",this.requires={swup:">=4"},this.defaults={head:!0,body:!0,optin:!1},this.options=void 0,this.options={...this.defaults,...t}}mount(){this.on("content:replace",this.runScripts)}runScripts(){const{head:t,body:e,optin:n}=this.options,r=this.getScope({head:t,body:e});if(!r)return;const o=Array.from(r.querySelectorAll(n?"script[data-swup-reload-script]":"script:not([data-swup-ignore-script])"));o.forEach(t=>this.runScript(t)),this.swup.log(`Executed ${o.length} scripts.`)}runScript(t){const e=document.createElement("script");for(const{name:n,value:r}of t.attributes)e.setAttribute(n,r);return e.textContent=t.textContent,t.replaceWith(e),e}getScope(t){let{head:e,body:n}=t;return e&&n?document:e?document.head:n?document.body:null}}});
//# sourceMappingURL=index.umd.js.map
