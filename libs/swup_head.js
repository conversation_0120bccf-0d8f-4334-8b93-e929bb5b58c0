!function(e, t) {
    "object" == typeof exports && "undefined" != typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define(t) : (e || self).SwupHeadPlugin = t()
}(this, function() {
    function e() {
        return e = Object.assign ? Object.assign.bind() : function(e) {
            for (var t = 1; t < arguments.length; t++) {
                var n = arguments[t];
                for (var r in n)
                    Object.prototype.hasOwnProperty.call(n, r) && (e[r] = n[r])
            }
            return e
        }
        ,
        e.apply(this, arguments)
    }
    const t = e => String(e).split(".").map(e => String(parseInt(e || "0", 10))).concat(["0", "0"]).slice(0, 3).join(".");
    class n {
        constructor() {
            this.isSwupPlugin = !0,
            this.swup = void 0,
            this.version = void 0,
            this.requires = {},
            this.handlersToUnregister = []
        }
        mount() {}
        unmount() {
            this.handlersToUnregister.forEach(e => e()),
            this.handlersToUnregister = []
        }
        _beforeMount() {
            if (!this.name)
                throw new Error("You must define a name of plugin when creating a class.")
        }
        _afterUnmount() {}
        _checkRequirements() {
            return "object" != typeof this.requires || Object.entries(this.requires).forEach( ([e,n]) => {
                if (!function(e, n, r) {
                    const s = function(e, t) {
                        var n;
                        if ("swup" === e)
                            return null != (n = t.version) ? n : "";
                        {
                            var r;
                            const n = t.findPlugin(e);
                            return null != (r = null == n ? void 0 : n.version) ? r : ""
                        }
                    }(e, r);
                    return !!s && ( (e, n) => n.every(n => {
                        const [,r,s] = n.match(/^([\D]+)?(.*)$/) || [];
                        var o, i;
                        return ( (e, t) => {
                            const n = {
                                "": e => 0 === e,
                                ">": e => e > 0,
                                ">=": e => e >= 0,
                                "<": e => e < 0,
                                "<=": e => e <= 0
                            };
                            return (n[t] || n[""])(e)
                        }
                        )((i = s,
                        o = t(o = e),
                        i = t(i),
                        o.localeCompare(i, void 0, {
                            numeric: !0
                        })), r || ">=")
                    }
                    ))(s, n)
                }(e, n = Array.isArray(n) ? n : [n], this.swup)) {
                    const t = `${e} ${n.join(", ")}`;
                    throw new Error(`Plugin version mismatch: ${this.name} requires ${t}`)
                }
            }
            ),
            !0
        }
        on(e, t, n={}) {
            var r;
            t = !(r = t).name.startsWith("bound ") || r.hasOwnProperty("prototype") ? t.bind(this) : t;
            const s = this.swup.hooks.on(e, t, n);
            return this.handlersToUnregister.push(s),
            s
        }
        once(t, n, r={}) {
            return this.on(t, n, e({}, r, {
                once: !0
            }))
        }
        before(t, n, r={}) {
            return this.on(t, n, e({}, r, {
                before: !0
            }))
        }
        replace(t, n, r={}) {
            return this.on(t, n, e({}, r, {
                replace: !0
            }))
        }
        off(e, t) {
            return this.swup.hooks.off(e, t)
        }
    }
    function r(e) {
        return "title" !== e.localName && !e.matches("[data-swup-theme]")
    }
    function s(e, t) {
        return e.outerHTML === t.outerHTML
    }
    function o(e) {
        return e.matches("link[rel=stylesheet][href]")
    }
    return class extends n {
        constructor(e) {
            void 0 === e && (e = {}),
            super();
            const t = this;
            this.name = "SwupHeadPlugin",
            this.requires = {
                swup: ">=4"
            },
            this.defaults = {
                persistTags: !1,
                persistAssets: !1,
                awaitAssets: !1,
                timeout: 3e3
            },
            this.options = void 0,
            this.updateHead = function(e, n) {
                let {page: {html: i}} = n;
                try {
                    const e = (new DOMParser).parseFromString(i, "text/html")
                      , {removed: n, added: l} = function(e, t, n) {
                        let {shouldPersist: o=( () => !1)} = void 0 === n ? {} : n;
                        const i = Array.from(e.children)
                          , u = Array.from(t.children)
                          , a = (l = i,
                        u.reduce( (e, t, n) => (l.some(e => s(t, e)) || e.push({
                            el: t,
                            index: n
                        }),
                        e), []));
                        var l;
                        const h = function(e, t) {
                            return e.reduce( (e, n) => (t.some(e => s(n, e)) || e.push({
                                el: n
                            }),
                            e), [])
                        }(i, u);
                        return h.reverse().filter(e => {
                            let {el: t} = e;
                            return r(t)
                        }
                        ).filter(e => {
                            let {el: t} = e;
                            return !o(t)
                        }
                        ).forEach(t => {
                            let {el: n} = t;
                            return e.removeChild(n)
                        }
                        ),
                        a.filter(e => {
                            let {el: t} = e;
                            return r(t)
                        }
                        ).forEach(t => {
                            let {el: n, index: r=0} = t;
                            e.insertBefore(n, e.children[r + 1] || null)
                        }
                        ),
                        {
                            removed: h.map(e => {
                                let {el: t} = e;
                                return t
                            }
                            ),
                            added: a.map(e => {
                                let {el: t} = e;
                                return t
                            }
                            )
                        }
                    }(document.head, e.head, {
                        shouldPersist: e => t.isPersistentTag(e)
                    });
                    t.swup.log(`Removed ${n.length} / added ${l.length} tags in head`);
                    const h = (u = document.documentElement).lang !== (a = e.documentElement).lang ? (u.lang = a.lang,
                    u.lang) : null;
                    h && t.swup.log(`Updated lang attribute: ${h}`);
                    const c = function() {
                        if (t.options.awaitAssets) {
                            const n = (void 0 === (e = t.options.timeout) && (e = 0),
                            l.filter(o).map(t => function(e, t) {
                                void 0 === t && (t = 0);
                                const n = t => {
                                    (e => {
                                        let {href: t} = e;
                                        return Array.from(document.styleSheets).map(e => {
                                            let {href: t} = e;
                                            return t
                                        }
                                        ).includes(t)
                                    }
                                    )(e) ? t() : setTimeout( () => n(t), 10)
                                }
                                ;
                                return new Promise(e => {
                                    n(e),
                                    t > 0 && setTimeout(e, t)
                                }
                                )
                            }(t, e)))
                              , r = function() {
                                if (n.length)
                                    return t.swup.log(`Waiting for ${n.length} assets to load`),
                                    Promise.resolve(Promise.all(n)).then(function() {})
                            }();
                            if (r && r.then)
                                return r.then(function() {})
                        }
                        var e
                    }();
                    return Promise.resolve(c && c.then ? c.then(function() {}) : void 0)
                } catch (e) {
                    return Promise.reject(e)
                }
                var u, a
            }
            ,
            this.options = {
                ...this.defaults,
                ...e
            },
            this.options.persistAssets && !this.options.persistTags && (this.options.persistTags = "link[rel=stylesheet], script[src], style")
        }
        mount() {
            this.before("content:replace", this.updateHead)
        }
        isPersistentTag(e) {
            const {persistTags: t} = this.options;
            return "function" == typeof t ? t(e) : "string" == typeof t ? e.matches(t) : Boolean(t)
        }
    }
});
