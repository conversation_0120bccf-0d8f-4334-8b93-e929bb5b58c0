$(document).ready(function(){
  $(document).on("initPage", function () {
    setAnchorPoints();
  });
});

function setAnchorPoints() {
  $(".anchorMenu").each(function () {
    var $anchorMenu = $(this);
    $($anchorMenu).html("");
    $("[data-anchor]").each(function () {
      const $section = $(this);
      const anchorName = $section.data("anchor");

      const $menuItem = $(`<a href="#${anchorName}" class="menu-item">${anchorName}</a>`);
      $anchorMenu.append($menuItem);
    });

    $anchorMenu.on("click touchend", ".menu-item", function (event) {
      const targetAnchor = $(this).attr("href");
      if (!targetAnchor) return;

      event.preventDefault();
      const targetID = targetAnchor.slice(1);
      const $targetSection = $(`[data-anchor="${targetID}"]`);

      if ($targetSection.length) {
        scroller.scrollTo($targetSection[0], {
          offset: 0,
          duration: 1.2,
          easing: (t) => 1 - Math.pow(1 - t, 4),
        });
      }
    });
  });

  $(document).on("click touchend", ".button", function (event) {
    const targetAnchor = $(this).attr("href");
    if (!targetAnchor) return;

    event.preventDefault();
    const targetID = targetAnchor.slice(1);
    const $targetSection = $(`[data-anchor="${targetID}"]`);

    if ($targetSection.length) {
      scroller.scrollTo($targetSection[0], {
        offset: 0,
        duration: 1.2,
        easing: (t) => 1 - Math.pow(1 - t, 4),
      });
    }
  });
}
