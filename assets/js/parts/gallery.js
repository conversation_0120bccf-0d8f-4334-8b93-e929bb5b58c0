$(document).ready(function () {
  $(document).on("initPage", function () {
    initializeGalleryPopups();
  });
});
const images = [];
var currentIndex = 0;

function initializeGalleryPopups() {
  images.length = 0; // Zorg ervoor dat de array niet onnodig gevuld wordt

  $('[data-gallery]').each(function () {
    const imageUrl = $(this).data('image');
    if (imageUrl) {
      images.push(imageUrl);
    }
  });



  $('[data-gallery]').off("click").on("click", function () {
    const index = $(this).data("index");
    const id = $(this).data("gallery-id");
    openOverlay(id, index, images);
    $("#header").addClass("hide");
    scroller.stop();
  });

  $(document).off("click", ".overlay .close, .overlay .background").on("click", ".overlay .close, .overlay .background", function () {
    $('.overlay').fadeOut();
    $("#header").removeClass("hide");
    scroller.start();
  });

  $(document).off("click", ".overlay .prev").on("click", ".overlay .prev", function () {
    currentIndex = (currentIndex > 0) ? currentIndex - 1 : images.length - 1;
    $('#overlayImg').attr('src', images[currentIndex]);
  });

  $(document).off("click", ".overlay .next").on("click", ".overlay .next", function () {
    currentIndex = (currentIndex < images.length - 1) ? currentIndex + 1 : 0;
    $('#overlayImg').attr('src', images[currentIndex]);
  });
}

function openOverlay(id, index, images) {
  currentIndex = parseInt(index, 10);
  $('.overlay').each(function () {
    if ($(this).data("gallery-id") === id) {
      $(this).fadeIn();
      $(this).find(".overlayImage").html("<img id='overlayImg' src='" + images[currentIndex] + "' />");
    }
  });
}

