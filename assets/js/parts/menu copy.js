var menuIsOpen = false;

var container;
var oldHeight;
var newHeight;
var header
var menuDisabled = false;

$(document).ready(function(){
  var menu = $("#menu");
  header = $("header");
  gsap.registerPlugin(CustomEase);
  CustomEase.create(
    "menubackground",
    "M0,0 C0.83,0 0.17,1 1,1"
  );

  $(document).on("click", "#burger, #menu .hamburger, .blurredOverlay, #menu .innerContent li a", function(e){
    if (!menuDisabled) {
      scroller.stop();
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu();
      } else {
        openMenu();
        menuIsOpen = true;
      }
    }
  });

  $(document).on("blur", "#pageContainer", function(e){
    if (!menuDisabled) {
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu("header #menu");
      }
    }
  });

  $(document).on("mouseover", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").addClass("hover");
    $(item).addClass("active");
  });

  $(document).on("mouseleave", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").removeClass("hover");
  });
});

function openMenu() {
  scroller.stop();
  $(menu).toggleClass("active");
  $(header).toggleClass("menuOpened");
  $(".blurredOverlay").toggleClass("active");
  $(menu).parents("header").find(".socials").toggleClass("active");
  setTimeout(function(){
    $(menu).find(".innerContent").addClass("showContent");
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}

function closeMenu() {
  $(menu).find(".innerContent").removeClass("showContent");
  setTimeout(function(){
    $(menu).toggleClass("active");
    $(header).toggleClass("menuOpened");
    $(".blurredOverlay").toggleClass("active");
    $(menu).parents("header").find(".socials").toggleClass("active");
    scroller.start();
  }, 300);
  setTimeout(function(){
    menuDisabled = false;
  }, 600);
}
