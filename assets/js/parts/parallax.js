var parallaxLoadOffset = 1000;
var parallaxIndicator = 20;
$(document).ready(function(){
    $(document).on("initPage", function(){
      if($(window).width() < $(window).height()){
          parallaxIndicator = 30;
      }
      changeparallaxElements();
    });

});

function changeparallaxElements() {
    $("[data-parallax]").each(function(i, el){
        ScrollTrigger.create({
            trigger: $(el),
            start: "0% 100%",
            end: "100% 0%",
            onUpdate(self){
                if(self.isActive){
                    if($(el).data("scroll-position") == "top"){
                        if(($(window).scrollTop() / parallaxIndicator) * $(el).data("parallax-speed") < 0){
                            var position = Math.abs((currentScrollY / (parallaxIndicator * 2)) * $(el).data("parallax-speed"));
                        } else {
                            var position = -Math.abs((currentScrollY / (parallaxIndicator * 2)) * $(el).data("parallax-speed"));
                        }
                        if($(el).data("scroll-direction") == "horizontal"){
                            gsap.to(el, 0, {x: position, force3D:true});
                        } else {
                            gsap.to(el, 0, {y: position, force3D:true});
                        }
                    } else {
                        var position = (($(el).offset().top - currentScrollY - (($(window).height() / 2) - ($(el).height() / 2))) / parallaxIndicator) * $(el).data("parallax-speed")
                        if($(el).data("scroll-direction") == "horizontal"){
                            gsap.to(el, 0, {x: position, force3D:true});
                        } else {
                            gsap.to(el, 0, {y: position, force3D:true});
                        }
                    }

                }
            }
        });
    });
}
