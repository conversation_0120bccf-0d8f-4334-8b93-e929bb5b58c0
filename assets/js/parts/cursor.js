$(document).ready(function(){
  $(document).on("initPage", function () {
    if (!$("body.touch").length > 0 && $(".backgroundCursor").length > 0) {
        backgroundCursorInit();
    }
    if (!$("body.touch").length > 0 && $(".mainCursor").length > 0) {
        mainCursorInit();
    }
  });
});

function backgroundCursorInit() {
    if ($(window).outerWidth(true) < 580) {$(".backgroundCursor").remove(); return; }
    const $cursor = $(".backgroundCursor");

    $(window).on("mousemove", function (e) {
        const x = e.pageX;
        const y = e.pageY - $(window).scrollTop();

        gsap.to($cursor, {
            x: x,
            y: y,
            duration: 0.2,
            ease: "power2.out"
        });
    });

    $(document).on("mouseenter", "[data-show-cursor]", function () {
        $cursor.addClass("show");
    }).on("mouseleave", "[data-show-cursor]", function () {
        $cursor.removeClass("show");
    });
}

function mainCursorInit() {
    if ($(window).outerWidth(true) < 580) {$(".mainCursor").remove(); return; }
    const $cursor = $(".mainCursor");

    $(window).on("mousemove", function (e) {
        const x = e.pageX;
        const y = e.pageY - $(window).scrollTop();

        gsap.to($cursor, {
            x: x,
            y: y,
            duration: 0.2,
            ease: "power2.out"
        });
    });

    $(document).on("mouseenter", "[data-show-mouse]", function () {
        $cursor.addClass("show");
    }).on("mouseleave", "[data-show-mouse]", function () {
        $cursor.removeClass("show");
    });
}
