// out: false
@import '../vw_values.less';
@import '../constants.less';

.backgroundCursor {
    position: fixed;
    width: 60vw;
    height: 60vw;
    background: rgba(@primaryColor, .3);
    pointer-events: none;
    opacity: 0;
    .filter(blur(@vw50));
    transform: translate(-50%, -50%);
    pointer-events: none;
    .rounded(50%);
    top: 0;
    left: 0;
    -webkit-mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    mask-image: radial-gradient(rgba(0,0,0,1), rgba(0,0,0,0), rgba(0,0,0,0));
    transition: opacity 0.3s ease-out;
    &.show {
        opacity: .6;
    }
}

.mainCursor {
    position: fixed;
    width: @vw68;
    height: @vw68;
    line-height: @vw68;
    background: @hardWhite;
    color: @primaryColor;
    pointer-events: none;
    opacity: 0;
    transform: translate(-50%, -50%);
    pointer-events: none;
    .rounded(50%);
    top: -@vw34;
    left: -@vw34;
    text-align: center;
    font-size: @vw26;
    &.show {
      opacity: 1;
    }
}

@media all and (max-width: 1080px) {
  .mainCursor {
    width: 50vw;
    height: 50vw;
    .filter(blur(@vw50-1080));
  }
}

@media all and (max-width: 580px) {
  .mainCursor {
    width: 40vw;
    height: 40vw;
    .filter(blur(@vw50-580));
  }
}