// out: false
@import '../vw_values.less';
@import '../constants.less';

html {
    &:not(.touch) {
        .button {
            &:hover {
                padding-left: @vw68;
                padding-right: 0;
                .body {
                    color: @primaryColor;
                    background: transparent;
                }
                .ball {
                    &:first-child {
                        .transform(scale(1));
                        i {
                            &:first-child {
                                .transform(translate(-50%, -50%) rotate(45deg));
                            }
                            &:last-child {
                                .transform(translate(50%, -150%) scale(.5));
                            }
                        }
                    }
                    &:last-child {
                        .transform(scale(0));
                    }
                }
            }
        }
    }
}
.button {
  display: inline-table;
  border: none;
  background: transparent;
  cursor: pointer;
  font-family: "Technor", sans-serif;
  font-weight: 600;
  font-size: @vw22;
  font-style: normal;
  text-align: left;
  color: @almostWhite;
  text-decoration: none;
  position: relative;
  padding-right: @vw68;
  padding-left: 0;
  transition: padding-left .3s, padding-right .3s;
  * {
    cursor: pointer;
  }
  .body {
    position: relative;
    .rounded(@vw100);
    height: @vw68;
    line-height: @vw68;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    padding: 0 @vw26;
    background: @primaryColor;
    border: 1px solid @primaryColor;
    color: @hardWhite;
    transition: color .3s, background .3s;
    z-index: 2;
  }
  .ball {
    width: @vw68;
    height: @vw68;
    display: inline-block;
    .rounded(50%);
    color: @primaryColor;
    background: @hardWhite;
    position: absolute;
    top: 0;
    left: auto;
    transition: transform .3s;
    overflow: hidden;
    right: 0;
    .transform(scale(1));
    .transitionMore(transform, .3s);
    &:first-child {
        left: 0;
        right: auto;
        .transform(scale(0));
        transform-origin: left center;
        i {
            &:first-child {
                .transform(translate(-150%, 50%) scale(.5));
            }
        }
    }
    &:last-child {
        transform-origin: right center;
    }
    i {
      position: absolute;
      left: 50%;
      top: 50%;
      font-size: @vw26;
      .transform(translate(-50%, -50%));
      .transitionMore(transform, .6s, 0s, cubic-bezier(0.85, 0, 0.15, 1));
    }
  }
}