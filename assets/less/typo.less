
// out: false
@import 'vw_values.less';
@import 'constants.less'; 

.hugeTitle, .bigTitle, .biggerTitle {
  &.white {
    color: @almostWhite;
  }
}

.hugeTitle {
  font-size: @vw100 + @vw100 + @vw10;
  text-decoration: none;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: italic;
  .transitionMore(opacity, .3s);
  &.link {
    cursor: pointer;
    color: @primaryColor;
    &:hover {
      opacity: .6;
    }
    span {
      cursor: pointer;
    }
  }
}

.bigTitle {
  font-size: @vw100 + @vw8;
  letter-spacing: -6px;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: normal;
  &.compact {
    font-size: @vw100 + @vw30;
    text-transform: uppercase;
    letter-spacing: 0;
    font-family: "owners-xxnarrow", sans-serif;
    font-weight: 800;
    font-style: normal;
  }
}

.signatureTitle {
  font-size: @vw95;
  line-height: 1.2;
  font-family: "Absolute Beauty", sans-serif;
  font-weight: 100;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.mediumTitle {
  font-size: @vw70;
  letter-spacing: 0;
  font-family: "Technor", sans-serif;
  font-weight: 700;
  font-style: normal;
  line-height: 1;
  strong {
    font-family: 'Buster Brush', cursive;
    font-size: @vw68;
    font-weight: 300;
    font-style: normal;
    line-height: 1;
    color: @primaryColor;
  }
}

.normalTitle {
  font-size: @vw32;
  letter-spacing: 0;
  font-family: "Technor", sans-serif;
  font-weight: 500;
  font-style: normal;
  line-height: 1;
}

.subTitle {
  font-size: @vw36;
  line-height: 1.2;
  font-family: "Technor", sans-serif;
  font-weight: 400;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.smallTitle {
  font-size: @vw22;
  line-height: 1.2;
  font-family: "Technor", sans-serif;
  font-weight: 400;
  font-style: normal;
  &.primary {
    color: @primaryColor;
  }
  &.secondary {
    color: @secondaryColor;
  }
}

.text {
  &.white {
    p {
      color: @grey;
    }
  }
  &:not(:first-child) {
    margin-top: @vw20;
  }
  p {
    line-height: 1.5;
    font-weight: 300;
    &:not(:last-child) {
      margin-bottom: @vw22;
    }
  }
}

@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: @vw72-1080;
  }

  .bigTitle {
    font-size: @vw82-1080;
    &.compact {
      font-size: @vw70-1080;
    }
  }

  .mediumTitle {
    font-size: @vw50-1080;
  }

  .subTitle {
    font-size: @vw24-1080;
  }

  .tinyTitle {
    font-size: @vw16-1080;
  }

  .text {
    &.bigger {
      font-size: @vw22-1080;
      p {
        font-size: @vw22-1080;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-1080;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: @vw35-580;
  }

  .bigTitle {
    font-size: @vw70-580;
    &.compact {
      font-size: @vw70-580;
    }
  }

  .mediumTitle {
    font-size: @vw50-580;
  }

  .subTitle {
    font-size: @vw24-580;
  }

  .tinyTitle {
    font-size: @vw16-580;
  }

  .text {
    &.bigger {
      font-size: @vw22-580;
      p { 
        font-size: @vw22-580;
      }
    }
    &:not(:first-child) {
      margin-top: @vw20-580;
    }
    p {
      &:not(:last-child) {
        margin-bottom: @vw22-580;
      }
    }
  }
}
