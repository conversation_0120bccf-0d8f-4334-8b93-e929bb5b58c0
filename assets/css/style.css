/*
Theme Name: GOAT EFC  
Author: <PERSON>
Version: 1.0.0 
*/
* {
  box-sizing: border-box;
  cursor: default;
  letter-spacing: 0;
  margin: 0;
  padding: 0;
  position: relative;
}
*::selection {
  background: #ECBF6A;
  color: #C2C2B5;
}
*::-webkit-selection {
  background: #ECBF6A;
  color: #C2C2B5;
}
*:focus {
  outline: none;
}
html.lenis {
  height: auto;
}
.lenis.lenis-smooth {
  scroll-behavior: auto;
}
.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}
.lenis.lenis-stopped {
  overflow: hidden;
}
html {
  overflow-x: hidden;
}
body {
  background: #191919;
  color: #C2C2B5;
  font-family: "owners", sans-serif;
  font-weight: 400;
  font-style: normal;
  overflow: hidden;
  line-height: 1;
  font-size: 0.926vw;
}
body strong,
body em {
  font-weight: 700;
}
body p a {
  cursor: pointer;
  color: #C2C2B5;
  text-decoration: none;
  font-weight: 700;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
}
body p a:hover {
  opacity: .6;
}
body ul {
  line-height: 1.4;
}
img {
  pointer-events: none;
}
.lines {
  position: absolute;
  width: 100vw;
  height: 100%;
  top: 0;
  left: 0;
}
.lines .contentWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.lines .contentWrapper .innerWrapper {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: space-between;
}
.lines .contentWrapper .innerWrapper .line {
  width: 1px;
  height: 100%;
  background: #C2C2B5;
  opacity: 0.1;
  position: relative;
}
body.touch a:hover,
body.touch .button:hover {
  opacity: 1 !important;
  color: inherit !important;
  background: inherit !important;
  transform: none !important;
  transition: none !important;
}
body.touch .button:hover {
  background: #ECBF6A !important;
}
[data-scroll-section] {
  background: #C2C2B5;
}
[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}
section {
  margin: 13.889vw 0;
}
section:first-of-type {
  margin-top: 0;
  padding-top: 11.574vw;
}
section.white {
  padding: 5.787vw 0;
  border-radius: 1.736vw;
  color: #191919;
  background: #C2C2B5;
}
section.inview [data-lines] {
  visibility: visible;
}
section.inview [data-lines] .line:nth-child(100) .word {
  transition-delay: 15.4s;
}
section.inview [data-lines] .line:nth-child(99) .word {
  transition-delay: 15.25s;
}
section.inview [data-lines] .line:nth-child(98) .word {
  transition-delay: 15.1s;
}
section.inview [data-lines] .line:nth-child(97) .word {
  transition-delay: 14.95s;
}
section.inview [data-lines] .line:nth-child(96) .word {
  transition-delay: 14.8s;
}
section.inview [data-lines] .line:nth-child(95) .word {
  transition-delay: 14.65s;
}
section.inview [data-lines] .line:nth-child(94) .word {
  transition-delay: 14.5s;
}
section.inview [data-lines] .line:nth-child(93) .word {
  transition-delay: 14.35s;
}
section.inview [data-lines] .line:nth-child(92) .word {
  transition-delay: 14.2s;
}
section.inview [data-lines] .line:nth-child(91) .word {
  transition-delay: 14.05s;
}
section.inview [data-lines] .line:nth-child(90) .word {
  transition-delay: 13.9s;
}
section.inview [data-lines] .line:nth-child(89) .word {
  transition-delay: 13.75s;
}
section.inview [data-lines] .line:nth-child(88) .word {
  transition-delay: 13.6s;
}
section.inview [data-lines] .line:nth-child(87) .word {
  transition-delay: 13.45s;
}
section.inview [data-lines] .line:nth-child(86) .word {
  transition-delay: 13.3s;
}
section.inview [data-lines] .line:nth-child(85) .word {
  transition-delay: 13.15s;
}
section.inview [data-lines] .line:nth-child(84) .word {
  transition-delay: 13s;
}
section.inview [data-lines] .line:nth-child(83) .word {
  transition-delay: 12.85s;
}
section.inview [data-lines] .line:nth-child(82) .word {
  transition-delay: 12.7s;
}
section.inview [data-lines] .line:nth-child(81) .word {
  transition-delay: 12.55s;
}
section.inview [data-lines] .line:nth-child(80) .word {
  transition-delay: 12.4s;
}
section.inview [data-lines] .line:nth-child(79) .word {
  transition-delay: 12.25s;
}
section.inview [data-lines] .line:nth-child(78) .word {
  transition-delay: 12.1s;
}
section.inview [data-lines] .line:nth-child(77) .word {
  transition-delay: 11.95s;
}
section.inview [data-lines] .line:nth-child(76) .word {
  transition-delay: 11.8s;
}
section.inview [data-lines] .line:nth-child(75) .word {
  transition-delay: 11.65s;
}
section.inview [data-lines] .line:nth-child(74) .word {
  transition-delay: 11.5s;
}
section.inview [data-lines] .line:nth-child(73) .word {
  transition-delay: 11.35s;
}
section.inview [data-lines] .line:nth-child(72) .word {
  transition-delay: 11.2s;
}
section.inview [data-lines] .line:nth-child(71) .word {
  transition-delay: 11.05s;
}
section.inview [data-lines] .line:nth-child(70) .word {
  transition-delay: 10.9s;
}
section.inview [data-lines] .line:nth-child(69) .word {
  transition-delay: 10.75s;
}
section.inview [data-lines] .line:nth-child(68) .word {
  transition-delay: 10.6s;
}
section.inview [data-lines] .line:nth-child(67) .word {
  transition-delay: 10.45s;
}
section.inview [data-lines] .line:nth-child(66) .word {
  transition-delay: 10.3s;
}
section.inview [data-lines] .line:nth-child(65) .word {
  transition-delay: 10.15s;
}
section.inview [data-lines] .line:nth-child(64) .word {
  transition-delay: 10s;
}
section.inview [data-lines] .line:nth-child(63) .word {
  transition-delay: 9.85s;
}
section.inview [data-lines] .line:nth-child(62) .word {
  transition-delay: 9.7s;
}
section.inview [data-lines] .line:nth-child(61) .word {
  transition-delay: 9.55s;
}
section.inview [data-lines] .line:nth-child(60) .word {
  transition-delay: 9.4s;
}
section.inview [data-lines] .line:nth-child(59) .word {
  transition-delay: 9.25s;
}
section.inview [data-lines] .line:nth-child(58) .word {
  transition-delay: 9.1s;
}
section.inview [data-lines] .line:nth-child(57) .word {
  transition-delay: 8.95s;
}
section.inview [data-lines] .line:nth-child(56) .word {
  transition-delay: 8.8s;
}
section.inview [data-lines] .line:nth-child(55) .word {
  transition-delay: 8.65s;
}
section.inview [data-lines] .line:nth-child(54) .word {
  transition-delay: 8.5s;
}
section.inview [data-lines] .line:nth-child(53) .word {
  transition-delay: 8.35s;
}
section.inview [data-lines] .line:nth-child(52) .word {
  transition-delay: 8.2s;
}
section.inview [data-lines] .line:nth-child(51) .word {
  transition-delay: 8.05s;
}
section.inview [data-lines] .line:nth-child(50) .word {
  transition-delay: 7.9s;
}
section.inview [data-lines] .line:nth-child(49) .word {
  transition-delay: 7.75s;
}
section.inview [data-lines] .line:nth-child(48) .word {
  transition-delay: 7.6s;
}
section.inview [data-lines] .line:nth-child(47) .word {
  transition-delay: 7.45s;
}
section.inview [data-lines] .line:nth-child(46) .word {
  transition-delay: 7.3s;
}
section.inview [data-lines] .line:nth-child(45) .word {
  transition-delay: 7.15s;
}
section.inview [data-lines] .line:nth-child(44) .word {
  transition-delay: 7s;
}
section.inview [data-lines] .line:nth-child(43) .word {
  transition-delay: 6.85s;
}
section.inview [data-lines] .line:nth-child(42) .word {
  transition-delay: 6.7s;
}
section.inview [data-lines] .line:nth-child(41) .word {
  transition-delay: 6.55s;
}
section.inview [data-lines] .line:nth-child(40) .word {
  transition-delay: 6.4s;
}
section.inview [data-lines] .line:nth-child(39) .word {
  transition-delay: 6.25s;
}
section.inview [data-lines] .line:nth-child(38) .word {
  transition-delay: 6.1s;
}
section.inview [data-lines] .line:nth-child(37) .word {
  transition-delay: 5.95s;
}
section.inview [data-lines] .line:nth-child(36) .word {
  transition-delay: 5.8s;
}
section.inview [data-lines] .line:nth-child(35) .word {
  transition-delay: 5.65s;
}
section.inview [data-lines] .line:nth-child(34) .word {
  transition-delay: 5.5s;
}
section.inview [data-lines] .line:nth-child(33) .word {
  transition-delay: 5.35s;
}
section.inview [data-lines] .line:nth-child(32) .word {
  transition-delay: 5.2s;
}
section.inview [data-lines] .line:nth-child(31) .word {
  transition-delay: 5.05s;
}
section.inview [data-lines] .line:nth-child(30) .word {
  transition-delay: 4.9s;
}
section.inview [data-lines] .line:nth-child(29) .word {
  transition-delay: 4.75s;
}
section.inview [data-lines] .line:nth-child(28) .word {
  transition-delay: 4.6s;
}
section.inview [data-lines] .line:nth-child(27) .word {
  transition-delay: 4.45s;
}
section.inview [data-lines] .line:nth-child(26) .word {
  transition-delay: 4.3s;
}
section.inview [data-lines] .line:nth-child(25) .word {
  transition-delay: 4.15s;
}
section.inview [data-lines] .line:nth-child(24) .word {
  transition-delay: 4s;
}
section.inview [data-lines] .line:nth-child(23) .word {
  transition-delay: 3.85s;
}
section.inview [data-lines] .line:nth-child(22) .word {
  transition-delay: 3.7s;
}
section.inview [data-lines] .line:nth-child(21) .word {
  transition-delay: 3.55s;
}
section.inview [data-lines] .line:nth-child(20) .word {
  transition-delay: 3.4s;
}
section.inview [data-lines] .line:nth-child(19) .word {
  transition-delay: 3.25s;
}
section.inview [data-lines] .line:nth-child(18) .word {
  transition-delay: 3.1s;
}
section.inview [data-lines] .line:nth-child(17) .word {
  transition-delay: 2.95s;
}
section.inview [data-lines] .line:nth-child(16) .word {
  transition-delay: 2.8s;
}
section.inview [data-lines] .line:nth-child(15) .word {
  transition-delay: 2.65s;
}
section.inview [data-lines] .line:nth-child(14) .word {
  transition-delay: 2.5s;
}
section.inview [data-lines] .line:nth-child(13) .word {
  transition-delay: 2.35s;
}
section.inview [data-lines] .line:nth-child(12) .word {
  transition-delay: 2.2s;
}
section.inview [data-lines] .line:nth-child(11) .word {
  transition-delay: 2.05s;
}
section.inview [data-lines] .line:nth-child(10) .word {
  transition-delay: 1.9s;
}
section.inview [data-lines] .line:nth-child(9) .word {
  transition-delay: 1.75s;
}
section.inview [data-lines] .line:nth-child(8) .word {
  transition-delay: 1.6s;
}
section.inview [data-lines] .line:nth-child(7) .word {
  transition-delay: 1.45s;
}
section.inview [data-lines] .line:nth-child(6) .word {
  transition-delay: 1.3s;
}
section.inview [data-lines] .line:nth-child(5) .word {
  transition-delay: 1.15s;
}
section.inview [data-lines] .line:nth-child(4) .word {
  transition-delay: 1s;
}
section.inview [data-lines] .line:nth-child(3) .word {
  transition-delay: 0.85s;
}
section.inview [data-lines] .line:nth-child(2) .word {
  transition-delay: 0.7s;
}
section.inview [data-lines] .line:nth-child(1) .word {
  transition-delay: 0.55s;
}
section.inview [data-lines] .line .word {
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  -webkit-transition: transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
  -moz-transition: transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
  -o-transition: transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
  transition: transform 0.75s 0s cubic-bezier(0, 0.55, 0.45, 1);
}
section [data-lines] .line {
  position: relative;
  overflow: hidden;
}
section [data-lines] .line .word {
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -o-transform: translateY(100%);
  -ms-transform: translateY(100%);
  transform: translateY(100%);
  will-change: transform;
}
[data-lines] {
  visibility: hidden;
}
.contentWrapper {
  display: block;
  width: 100%;
  padding: 0 4.456vw;
}
.contentWrapper.smaller {
  padding: 0 13.426vw;
}
.noise {
  background-color: rgba(20, 20, 20, 0.3);
  background-position: 0;
  height: 100%;
  opacity: 1;
  pointer-events: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
}
@keyframes noise {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 100%;
  }
}
.textLink {
  display: inline-table;
  cursor: pointer;
  font-size: 1.273vw;
  line-height: 1.4;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: normal;
  color: #C2C2B5;
  text-transform: uppercase;
  text-decoration: none;
  -webkit-transition: color 0.3s 0s ease-out;
  -moz-transition: color 0.3s 0s ease-out;
  -o-transition: color 0.3s 0s ease-out;
  transition: color 0.3s 0s ease-out;
}
.textLink.bigger {
  font-size: 2.894vw;
}
.textLink.bigger .innerText {
  display: inline;
  padding-right: 1.736vw;
  width: calc(96.296%);
}
.textLink.bigger .arrows {
  width: 2.894vw;
  height: 2.894vw;
  font-size: 1.736vw;
  line-height: 2.894vw;
}
.textLink * {
  cursor: pointer;
}
.textLink:hover {
  color: #ECBF6A;
}
.textLink:hover .arrows {
  border-color: #ECBF6A;
}
.textLink:hover .arrows i:first-child {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.textLink:hover .arrows i:last-child {
  -webkit-transform: translate(50%, -150%) scale(0.5);
  -moz-transform: translate(50%, -150%) scale(0.5);
  -o-transform: translate(50%, -150%) scale(0.5);
  -ms-transform: translate(50%, -150%) scale(0.5);
  transform: translate(50%, -150%) scale(0.5);
}
.textLink .innerText {
  display: inline-block;
  vertical-align: middle;
  width: calc(98.032%);
  padding-right: 0.926vw;
}
.textLink .arrows {
  display: inline-block;
  vertical-align: middle;
  width: 1.968vw;
  height: 1.968vw;
  border: 1px solid #C2C2B5;
  line-height: 1.968vw;
  text-align: center;
  position: relative;
  overflow: hidden;
  -webkit-transition: border-color 0.3s 0s ease-out;
  -moz-transition: border-color 0.3s 0s ease-out;
  -o-transition: border-color 0.3s 0s ease-out;
  transition: border-color 0.3s 0s ease-out;
}
.textLink .arrows i {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -moz-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -o-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
}
.textLink .arrows i:first-child {
  -webkit-transform: translate(-150%, 50%) scale(0.5);
  -moz-transform: translate(-150%, 50%) scale(0.5);
  -o-transform: translate(-150%, 50%) scale(0.5);
  -ms-transform: translate(-150%, 50%) scale(0.5);
  transform: translate(-150%, 50%) scale(0.5);
}
.arrowButton {
  cursor: pointer;
  display: inline-block;
  border: 1px solid #C2C2B5;
  font-size: 1.852vw;
  text-align: center;
  line-height: 3.646vw;
  width: 3.646vw;
  height: 3.646vw;
  overflow: hidden;
  position: relative;
  vertical-align: middle;
  transition: color 0.3s, border-color 0.3s;
}
.arrowButton.prev:hover i:last-child {
  -webkit-transform: translate(-200%, -50%) scale(0.5);
  -moz-transform: translate(-200%, -50%) scale(0.5);
  -o-transform: translate(-200%, -50%) scale(0.5);
  -ms-transform: translate(-200%, -50%) scale(0.5);
  transform: translate(-200%, -50%) scale(0.5);
}
.arrowButton.prev i:first-child {
  -webkit-transform: translate(100%, -50%) scale(0.5);
  -moz-transform: translate(100%, -50%) scale(0.5);
  -o-transform: translate(100%, -50%) scale(0.5);
  -ms-transform: translate(100%, -50%) scale(0.5);
  transform: translate(100%, -50%) scale(0.5);
}
.arrowButton:hover {
  border-color: #ECBF6A;
  color: #ECBF6A;
}
.arrowButton:hover i:first-child {
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.arrowButton:hover i:last-child {
  -webkit-transform: translate(100%, -50%) scale(0.5);
  -moz-transform: translate(100%, -50%) scale(0.5);
  -o-transform: translate(100%, -50%) scale(0.5);
  -ms-transform: translate(100%, -50%) scale(0.5);
  transform: translate(100%, -50%) scale(0.5);
}
.arrowButton i {
  cursor: pointer;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -moz-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -o-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
}
.arrowButton i:first-child {
  -webkit-transform: translate(-200%, -50%) scale(0.5);
  -moz-transform: translate(-200%, -50%) scale(0.5);
  -o-transform: translate(-200%, -50%) scale(0.5);
  -ms-transform: translate(-200%, -50%) scale(0.5);
  transform: translate(-200%, -50%) scale(0.5);
}
#background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.dg.ac {
  z-index: 3 !important;
}
@media all and (max-width: 1080px) {
  body {
    font-size: 1.552vw;
  }
  section {
    margin: 10.345vw 0;
  }
  section:first-of-type {
    padding-top: 17.242vw;
  }
  section.white {
    padding: 8.621vw 0;
    border-radius: 2.586vw;
  }
  .contentWrapper {
    padding: 0 3.448vw;
  }
  .contentWrapper.smaller {
    padding: 0 6.207vw;
  }
  .button {
    padding: 1.034vw 1.724vw;
  }
  .button .innerText {
    width: calc(97.069%);
    padding-right: 1.379vw;
  }
  .button .arrows {
    width: 2.931vw;
    height: 2.931vw;
    font-size: 2.069vw;
    line-height: 2.931vw;
  }
  .textLink {
    font-size: 1.207vw;
  }
  .textLink .innerText {
    width: calc(97.069%);
    padding-right: 1.379vw;
  }
  .textLink.bigger {
    font-size: 4.31vw;
  }
  .textLink.bigger .innerText {
    padding-right: 2.586vw;
    width: calc(94.483%);
  }
  .textLink.bigger .arrows {
    width: 4.31vw;
    height: 4.31vw;
    font-size: 2.586vw;
    line-height: 4.31vw;
  }
  .textLink .arrows {
    width: 2.931vw;
    height: 2.931vw;
    line-height: 2.931vw;
  }
  .arrowButton {
    font-size: 2.759vw;
    line-height: 5.431vw;
    width: 5.431vw;
    height: 5.431vw;
  }
}
@media all and (max-width: 580px) {
  body {
    font-size: 3.793vw;
    line-height: 1.2;
  }
  section {
    margin: 20.689vw 0;
  }
  section:first-of-type {
    padding-top: 43.1025vw;
  }
  section.white {
    padding: 17.241vw 0;
    border-radius: 5.172vw;
  }
  .contentWrapper {
    padding: 0 3.793vw;
  }
  .contentWrapper.smaller {
    padding: 0 3.793vw;
  }
  .button {
    padding: 2.069vw 3.448vw;
  }
  .button .innerText {
    width: calc(94.138%);
    padding-right: 2.758vw;
  }
  .button .arrows {
    width: 5.862vw;
    height: 5.862vw;
    font-size: 4.137vw;
    line-height: 5.862vw;
  }
  .textLink {
    font-size: 4.137vw;
  }
  .textLink.bigger {
    font-size: 4.137vw;
  }
  .textLink.bigger .innerText {
    width: calc(94.138%);
    padding-right: 2.758vw;
  }
  .textLink.bigger .arrows {
    width: 5.862vw;
    height: 5.862vw;
    line-height: 5.862vw;
  }
  .textLink .innerText {
    width: calc(94.138%);
    padding-right: 2.758vw;
  }
  .textLink .arrows {
    width: 5.862vw;
    height: 5.862vw;
    line-height: 5.862vw;
  }
  .arrowButton {
    font-size: 5.517vw;
    line-height: 10.862vw;
    width: 10.862vw;
    height: 10.862vw;
  }
}
.hugeTitle.white,
.bigTitle.white,
.biggerTitle.white {
  color: #C2C2B5;
}
.hugeTitle {
  font-size: 12.153vw;
  text-decoration: none;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: italic;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.hugeTitle.link {
  cursor: pointer;
  color: #ECBF6A;
}
.hugeTitle.link:hover {
  opacity: .6;
}
.hugeTitle.link span {
  cursor: pointer;
}
.bigTitle {
  font-size: 6.25vw;
  letter-spacing: -6px;
  font-family: "owners-xwide", sans-serif;
  font-weight: 800;
  font-style: normal;
}
.bigTitle.compact {
  font-size: 7.523vw;
  text-transform: uppercase;
  letter-spacing: 0;
  font-family: "owners-xxnarrow", sans-serif;
  font-weight: 800;
  font-style: normal;
}
.signatureTitle {
  font-size: 5.498vw;
  line-height: 1.2;
  font-family: "Absolute Beauty", sans-serif;
  font-weight: 100;
  font-style: normal;
}
.signatureTitle.primary {
  color: #ECBF6A;
}
.signatureTitle.secondary {
  color: #63865F;
}
.mediumTitle {
  font-size: 4.051vw;
  letter-spacing: 0;
  font-family: "Technor", sans-serif;
  font-weight: 700;
  font-style: normal;
  line-height: 1;
}
.subTitle {
  font-size: 2.083vw;
  line-height: 1.2;
  font-family: "Technor", sans-serif;
  font-weight: 400;
  font-style: normal;
}
.subTitle.primary {
  color: #ECBF6A;
}
.subTitle.secondary {
  color: #63865F;
}
.tinyTitle {
  font-size: 0.926vw;
  line-height: 1.4;
  text-transform: uppercase;
}
.text.bigger {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.bigger p {
  font-size: 1.273vw;
  text-transform: uppercase;
}
.text.white p {
  color: #C2C2B5;
}
.text:not(:first-child) {
  margin-top: 1.157vw;
}
.text p {
  line-height: 1.2;
  font-weight: 100;
}
.text p:not(:last-child) {
  margin-bottom: 1.273vw;
}
@media all and (max-width: 1080px) {
  .hugeTitle {
    font-size: 6.207vw;
  }
  .bigTitle {
    font-size: 7.069vw;
  }
  .bigTitle.compact {
    font-size: 6.034vw;
  }
  .mediumTitle {
    font-size: 4.31vw;
  }
  .subTitle {
    font-size: 2.069vw;
  }
  .tinyTitle {
    font-size: 1.379vw;
  }
  .text.bigger {
    font-size: 1.897vw;
  }
  .text.bigger p {
    font-size: 1.897vw;
  }
  .text:not(:first-child) {
    margin-top: 1.724vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .hugeTitle {
    font-size: 6.034vw;
  }
  .bigTitle {
    font-size: 12.069vw;
  }
  .bigTitle.compact {
    font-size: 12.069vw;
  }
  .mediumTitle {
    font-size: 8.62vw;
  }
  .subTitle {
    font-size: 4.137vw;
  }
  .tinyTitle {
    font-size: 2.758vw;
  }
  .text.bigger {
    font-size: 3.793vw;
  }
  .text.bigger p {
    font-size: 3.793vw;
  }
  .text:not(:first-child) {
    margin-top: 3.448vw;
  }
  .text p:not(:last-child) {
    margin-bottom: 3.793vw;
  }
}
header {
  position: fixed;
  padding-top: 1.273vw;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 99;
}
header > .background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 17.361vw;
  pointer-events: none;
  z-index: -1;
  backdrop-filter: blur(2.894vw);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(0, 0, 0, 0);
  -webkit-mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
  mask-image: linear-gradient(#000000, rgba(0, 0, 0, 0));
}
header .innerMenu {
  display: inline-block;
  vertical-align: middle;
  margin-left: 9.259vw;
  list-style: none;
}
header .innerMenu li {
  display: inline-block;
}
header .innerMenu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw;
  cursor: pointer;
  color: #C2C2B5;
  text-decoration: none;
  transition: color .3s, transform .3s;
}
header .innerMenu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
header .innerMenu a:hover {
  color: #ECBF6A;
}
header .col {
  display: inline-block;
  width: 75%;
  vertical-align: middle;
  position: relative;
}
header .col:last-child {
  text-align: right;
  width: 25%;
  padding-right: 7.523vw;
}
header .col .logo {
  display: inline-block;
  width: 11.574vw;
  height: auto;
  vertical-align: middle;
  position: relative;
  opacity: 1;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
header .col .logo:hover {
  opacity: .4;
}
header .col .logo svg {
  height: auto;
  width: 100%;
  object-fit: contain;
}
header .col .socials {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  z-index: 10;
}
header .col .socials.active .social {
  color: #191919;
}
header .col .socials.active .social:hover {
  color: #ECBF6A;
}
header .col .socials .social {
  display: inline-block;
  padding: 0.579vw;
  cursor: pointer;
  color: #C2C2B5;
  text-decoration: none;
  vertical-align: middle;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
header .col .socials .social:hover {
  color: #ECBF6A;
}
header .col .button {
  display: inline-block;
  margin-left: 2.315vw;
  vertical-align: middle;
}
#menu {
  display: inline-block;
  padding: 1.157vw;
  cursor: pointer;
  position: absolute;
  right: 0;
  top: -0.694vw;
  color: #C2C2B5;
  font-size: 1.215vw;
  z-index: 9;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .background {
  height: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #C2C2B5;
  -webkit-transition: opacity 0.3s 0s ease-in;
  -moz-transition: opacity 0.3s 0s ease-in;
  -o-transition: opacity 0.3s 0s ease-in;
  transition: opacity 0.3s 0s ease-in;
}
#menu.active {
  color: #191919;
}
#menu.active .background {
  opacity: 1;
}
#menu.active .hamburger .border {
  background: #191919;
}
#menu.active .hamburger .border:nth-child(1) {
  -webkit-transform: translateY(0.347vw) rotate(-45deg);
  -moz-transform: translateY(0.347vw) rotate(-45deg);
  -o-transform: translateY(0.347vw) rotate(-45deg);
  -ms-transform: translateY(0.347vw) rotate(-45deg);
  transform: translateY(0.347vw) rotate(-45deg);
}
#menu.active .hamburger .border:nth-child(2) {
  width: 0%;
}
#menu.active .hamburger .border:nth-child(3) {
  -webkit-transform: translateY(-0.347vw) rotate(45deg);
  -moz-transform: translateY(-0.347vw) rotate(45deg);
  -o-transform: translateY(-0.347vw) rotate(45deg);
  -ms-transform: translateY(-0.347vw) rotate(45deg);
  transform: translateY(-0.347vw) rotate(45deg);
}
#menu.active .innerContent {
  display: block;
  width: 26.735vw;
}
#menu div,
#menu span {
  cursor: pointer;
  display: inline-block;
}
#menu .innerContent {
  display: none;
  padding: 2.315vw 0;
}
#menu .innerContent.showContent ul.hover li a {
  opacity: .2;
}
#menu .innerContent.showContent ul.hover li.active a {
  opacity: 1;
}
#menu .innerContent.showContent ul li {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
#menu .innerContent.showContent ul li:nth-child(2) {
  transition-delay: .15s;
}
#menu .innerContent.showContent ul li:nth-child(3) {
  transition-delay: .3s;
}
#menu .innerContent.showContent ul li:nth-child(4) {
  transition-delay: .45s;
}
#menu .innerContent.showContent ul li:nth-child(5) {
  transition-delay: .6s;
}
#menu .innerContent.showContent ul li:nth-child(6) {
  transition-delay: .75s;
}
#menu .innerContent > div {
  display: block;
}
#menu .innerContent .menu-primary-menu-container {
  padding-bottom: 1.157vw;
}
#menu .innerContent .menu-primary-menu-container ul li {
  font-size: 1.389vw;
  line-height: 1.4;
  font-family: "owners-xwide", sans-serif;
  font-weight: 500;
}
#menu .innerContent ul {
  list-style: none;
}
#menu .innerContent ul li {
  font-family: "owners", sans-serif;
  font-size: 1.215vw;
  font-weight: 600;
  padding-bottom: 0.579vw;
  line-height: 1.794vw;
  text-transform: uppercase;
  visibility: hidden;
  position: relative;
  opacity: 0;
  transform: translateY(1.157vw);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .innerContent ul li a {
  cursor: pointer;
  color: #191919;
  text-decoration: none;
  -webkit-transition: all 0.15s ease-out;
  -moz-transition: all 0.15s ease-out;
  -o-transition: all 0.15s ease-out;
  transition: all 0.15s ease-out;
  padding-right: 2.894vw;
}
#menu .innerContent ul li a:after {
  font-family: "icomoon";
  content: '\e900';
  position: absolute;
  right: 0;
  top: -0.116vw;
  -webkit-transition: transform 0.3s 0s ease-out;
  -moz-transition: transform 0.3s 0s ease-out;
  -o-transition: transform 0.3s 0s ease-out;
  transition: transform 0.3s 0s ease-out;
}
#menu .innerContent ul li a:hover:after {
  transform: scale(0.8);
}
#menu .hamburger {
  cursor: pointer;
  margin-left: 0.81vw;
  display: inline-block;
  height: 0.81vw;
  width: 0.926vw;
}
#menu .hamburger .border {
  position: absolute;
  display: block;
  height: 2px;
  width: 100%;
  border-radius: 0.116vw;
  background: #C2C2B5;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
#menu .hamburger .border:first-child {
  top: 0;
}
#menu .hamburger .border:nth-child(2) {
  top: 50%;
  transform: translateY(-50%);
}
#menu .hamburger .border:nth-child(3) {
  bottom: 0;
  top: auto;
}
@media all and (max-width: 1080px) {
  header > .background {
    height: 25.863vw;
    backdrop-filter: blur(4.31vw);
  }
  header .col {
    width: 66.6666%;
  }
  header .col:last-child {
    width: 33.3333%;
  }
  header .innerMenu {
    margin-left: 13.793vw;
  }
  header .innerMenu a {
    padding: 0.862vw;
  }
  header .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
  header .col:last-child {
    padding-right: 11.207vw;
  }
  header .col .logo {
    width: 9.655vw;
    height: 14.655vw;
    top: -1.897vw;
  }
  header .col .logo svg {
    bottom: 2.155vw;
  }
  header .col .socials .social {
    padding: 0.862vw;
  }
  header .col .button {
    margin-left: 3.448vw;
  }
  #menu {
    padding: 1.724vw;
    top: -1.034vw;
    font-size: 1.81vw;
  }
  #menu.active .hamburger .border:nth-child(1) {
    -webkit-transform: translateY(0.517vw) rotate(-45deg);
    -moz-transform: translateY(0.517vw) rotate(-45deg);
    -o-transform: translateY(0.517vw) rotate(-45deg);
    -ms-transform: translateY(0.517vw) rotate(-45deg);
    transform: translateY(0.517vw) rotate(-45deg);
  }
  #menu.active .hamburger .border:nth-child(3) {
    -webkit-transform: translateY(-0.517vw) rotate(45deg);
    -moz-transform: translateY(-0.517vw) rotate(45deg);
    -o-transform: translateY(-0.517vw) rotate(45deg);
    -ms-transform: translateY(-0.517vw) rotate(45deg);
    transform: translateY(-0.517vw) rotate(45deg);
  }
  #menu.active .innerContent {
    width: 39.827vw;
  }
  #menu .innerContent {
    padding: 3.448vw 0;
  }
  #menu .innerContent .menu-primary-menu-container {
    padding-bottom: 1.724vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li {
    font-size: 1.897vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a {
    padding-right: 4.31vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a:after {
    top: -0.172vw;
  }
  #menu .innerContent ul li {
    font-size: 1.81vw;
    padding-bottom: 0.862vw;
    line-height: 2.672vw;
    transform: translateY(1.724vw);
  }
  #menu .hamburger {
    margin-left: 1.207vw;
    height: 1.207vw;
    width: 1.379vw;
  }
  #menu .hamburger .border {
    border-radius: 0.172vw;
  }
}
@media all and (max-width: 580px) {
  header {
    padding-top: 4.137vw;
  }
  header > .background {
    height: 51.723vw;
    backdrop-filter: blur(8.62vw);
  }
  header .innerMenu {
    display: none;
  }
  header .col {
    width: 25%;
  }
  header .col:last-child {
    width: 75%;
    padding-right: 24.138vw;
  }
  header .col .logo {
    width: 12.069vw;
    height: 15.517vw;
    top: -8.62vw;
  }
  header .col .logo svg {
    bottom: 4.31vw;
  }
  header .col .socials .social {
    padding: 1.724vw;
  }
  header .col .socials .social:not(:last-child) {
    margin-right: 2.758vw;
  }
  header .col .button {
    margin-left: 6.897vw;
  }
  #menu {
    padding: 3.448vw;
    top: -2.069vw;
    font-size: 3.62vw;
  }
  #menu.active .hamburger .border:nth-child(1) {
    -webkit-transform: translateY(1.034vw) rotate(-45deg);
    -moz-transform: translateY(1.034vw) rotate(-45deg);
    -o-transform: translateY(1.034vw) rotate(-45deg);
    -ms-transform: translateY(1.034vw) rotate(-45deg);
    transform: translateY(1.034vw) rotate(-45deg);
  }
  #menu.active .hamburger .border:nth-child(3) {
    -webkit-transform: translateY(-1.034vw) rotate(45deg);
    -moz-transform: translateY(-1.034vw) rotate(45deg);
    -o-transform: translateY(-1.034vw) rotate(45deg);
    -ms-transform: translateY(-1.034vw) rotate(45deg);
    transform: translateY(-1.034vw) rotate(45deg);
  }
  #menu.active .innerContent {
    width: 79.655vw;
  }
  #menu .innerContent {
    padding: 6.897vw 0;
  }
  #menu .innerContent .menu-primary-menu-container {
    padding-bottom: 3.448vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li {
    font-size: 4.482vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a {
    padding-right: 8.62vw;
  }
  #menu .innerContent .menu-primary-menu-container ul li a:after {
    top: -0.344vw;
  }
  #menu .innerContent ul li {
    font-size: 3.62vw;
    padding-bottom: 1.724vw;
    line-height: 8.62vw;
    transform: translateY(3.448vw);
  }
  #menu .hamburger {
    margin-left: 3.793vw;
    height: 2.414vw;
    width: 3.793vw;
  }
  #menu .hamburger .border {
    border-radius: 0.344vw;
  }
}
body.touch .footer .bigTitle a:hover {
  padding-left: 0;
  padding-right: 7.758vw;
  color: #191919 !important;
}
body.touch .footer .bigTitle a:hover:after {
  width: 0%;
}
body.touch .footer .bigTitle a:hover i {
  opacity: 0;
}
.footer {
  background: #C2C2B5;
  padding: 4.051vw 0 1.157vw 0;
  position: relative;
  overflow: hidden;
}
.footer .backgroundWrapper {
  width: 60vw;
  position: absolute;
  height: 60vw;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  top: 50%;
  opacity: 1;
  left: 0;
}
.footer .backgroundWrapper .background {
  opacity: 1;
  position: absolute;
  animation: moveBackground 10s infinite ease-in-out alternate;
  top: 0;
  left: 0;
  width: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  height: 100%;
  background: #FFFFFF;
  -webkit-mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
  mask-image: radial-gradient(#000000, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
}
.footer .bottomFooter {
  color: #191919;
  margin-top: 1.273vw;
}
.footer .bottomFooter .col {
  display: inline-block;
  vertical-align: middle;
  width: 50%;
}
.footer .bottomFooter .col:last-child {
  text-align: right;
}
.footer .logo {
  width: 1.389vw;
  display: inline-block;
  vertical-align: middle;
}
.footer .logo svg {
  object-fit: contain;
  width: 100%;
  height: auto;
}
.footer .innerMenu {
  display: inline-block;
  margin-left: 4.63vw;
  vertical-align: middle;
  list-style: none;
}
.footer .innerMenu li {
  display: inline-block;
}
.footer .innerMenu a {
  display: inline-block;
  vertical-align: middle;
  padding: 0.579vw;
  cursor: pointer;
  color: #191919;
  text-decoration: none;
  transition: color .3s, transform .3s;
}
.footer .innerMenu a:not(:last-of-type) {
  margin-right: 1.273vw;
}
.footer .innerMenu a:hover {
  color: #ECBF6A;
}
@media all and (max-width: 1080px) {
  .footer {
    padding: 6.034vw 0 1.724vw 0;
  }
  .footer .cols {
    margin-left: -1.724vw;
    width: calc(103.448%);
  }
  .footer .cols .col {
    margin: 0 1.724vw;
    width: calc(29.8853%);
  }
  .footer img {
    width: 12.931vw;
    margin-top: 3.448vw;
  }
  .footer .bigTitle a {
    padding-right: 3.879vw;
    padding-bottom: 0.862vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 3.879vw;
  }
  .footer .bigTitle a i {
    left: -2.586vw;
  }
  .footer .logo {
    width: 2.069vw;
  }
  .footer .innerMenu {
    margin-left: 6.897vw;
  }
  .footer .innerMenu a {
    padding: 0.862vw;
  }
  .footer .innerMenu a:not(:last-of-type) {
    margin-right: 1.897vw;
  }
  .footer .bottomFooter {
    margin-top: 1.897vw;
  }
}
@media all and (max-width: 580px) {
  .footer {
    padding: 12.069vw 0 3.448vw 0;
  }
  .footer .backgroundWrapper {
    width: 120vw;
    height: 120vw;
  }
  .footer .cols {
    margin-left: -3.448vw;
    width: calc(106.897%);
  }
  .footer .cols .col {
    margin: 0 3.448vw;
    width: calc(43.103%);
  }
  .footer .cols .col:nth-child(2) {
    display: none;
  }
  .footer img {
    width: 25.861vw;
    margin-top: 6.897vw;
  }
  .footer .bigTitle a {
    padding-right: 7.758vw;
    padding-bottom: 1.724vw;
    padding-left: 0;
  }
  .footer .bigTitle a:after {
    height: 2px;
  }
  .footer .bigTitle a:hover {
    padding-left: 7.758vw;
  }
  .footer .bigTitle a i {
    left: -5.172vw;
  }
  .footer .logo {
    width: 4.137vw;
  }
  .footer .bottomFooter {
    margin-top: 8.62vw;
    font-size: 3.793vw;
  }
  .footer .innerMenu {
    display: none;
  }
}
@keyframes moveBackground {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10vw, -5vw);
  }
  50% {
    transform: translate(-10vw, 5vw);
  }
  75% {
    transform: translate(5vw, -10vw);
  }
  100% {
    transform: translate(0, 0);
  }
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.overlay .background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
}
.overlay .overlayContent {
  height: 100%;
  width: 100%;
  position: relative;
  text-align: center;
}
.overlay .overlayImage {
  align-items: center;
  justify-content: center;
  display: flex;
  height: 100%;
  width: 100%;
}
.overlay img {
  display: block;
  margin: auto;
  max-width: 80%;
  max-height: 80%;
}
.overlay .close {
  position: absolute;
  top: 20px;
  right: 30px;
  font-size: 30px;
  color: white;
  cursor: pointer;
  z-index: 2;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.overlay .close:hover {
  transform: rotate(45deg);
}
.overlay .navButton {
  cursor: pointer;
  height: 2.894vw;
  width: 2.894vw;
  line-height: 3.009vw;
  background: #ECBF6A;
  text-align: center;
  font-size: 1.273vw;
  color: #FFFFFF;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out;
  -o-transition: all 0.3s ease-out;
  transition: all 0.3s ease-out;
}
.overlay .navButton:hover {
  background: #191919;
}
.overlay .navButton.prev {
  left: 5.787vw;
}
.overlay .navButton.next {
  right: 5.787vw;
}
@media screen and (max-width: 1080px) {
  .overlay .navButton {
    height: 4.31vw;
    width: 4.31vw;
    line-height: 4.483vw;
    font-size: 1.897vw;
  }
  .overlay .navButton.prev {
    left: 8.621vw;
  }
  .overlay .navButton.next {
    right: 8.621vw;
  }
}
@media screen and (max-width: 580px) {
  .overlay .navButton {
    height: 8.62vw;
    width: 8.62vw;
    line-height: 8.965vw;
    font-size: 3.793vw;
  }
  .overlay .navButton.prev {
    left: 0;
  }
  .overlay .navButton.next {
    right: 0;
  }
}
.signatureDD {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}
.signatureDD:hover {
  opacity: .4;
  transition: opacity .3s;
}
.signatureDD .linkDD {
  color: #000000;
  cursor: pointer;
  font-size: 0.926vw;
  display: inline-block;
  text-decoration: none;
}
.signatureDD .innerTextDD,
.signatureDD .svgWrap {
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
}
.signatureDD .svgWrap {
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  width: 4.63vw;
  height: auto;
}
.signatureDD .svgWrap svg {
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  height: auto;
  object-fit: contain;
}
@media all and (max-width: 1080px) {
  .signatureDD .linkDD {
    font-size: 1.379vw;
  }
  .signatureDD .svgWrap {
    width: 6.897vw;
  }
}
@media all and (max-width: 580px) {
  .signatureDD .linkDD {
    font-size: 4.137vw;
  }
  .signatureDD .svgWrap {
    width: 22.414vw;
  }
}
html:not(.touch) .button:hover {
  padding-left: 3.935vw;
  padding-right: 3.935vw;
}
html:not(.touch) .button:hover .body {
  color: #ECBF6A;
  background: transparent;
}
html:not(.touch) .button:hover .ball:first-child {
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
}
html:not(.touch) .button:hover .ball:first-child i:first-child {
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
html:not(.touch) .button:hover .ball:first-child i:last-child {
  -webkit-transform: translate(50%, -150%) scale(0.5);
  -moz-transform: translate(50%, -150%) scale(0.5);
  -o-transform: translate(50%, -150%) scale(0.5);
  -ms-transform: translate(50%, -150%) scale(0.5);
  transform: translate(50%, -150%) scale(0.5);
}
html:not(.touch) .button:hover .ball:last-child {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
}
.button {
  display: inline-table;
  border: none;
  background: transparent;
  cursor: pointer;
  font-family: "Technor", sans-serif;
  font-weight: 600;
  font-size: 1.273vw;
  font-style: normal;
  text-align: left;
  color: #C2C2B5;
  text-decoration: none;
  position: relative;
  padding-right: 3.935vw;
  padding-left: 0;
  transition: padding-left 0.3s, padding-right 0.3s;
}
.button * {
  cursor: pointer;
}
.button .body {
  position: relative;
  -webkit-border-radius: 5.787vw;
  -moz-border-radius: 5.787vw;
  border-radius: 5.787vw;
  height: 3.935vw;
  line-height: 3.935vw;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  padding: 0 1.505vw;
  background: #ECBF6A;
  border: 1px solid #ECBF6A;
  color: #FFFFFF;
  transition: color .3s, background .3s;
  z-index: 2;
}
.button .ball {
  width: 3.935vw;
  height: 3.935vw;
  display: inline-block;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  color: #ECBF6A;
  background: #FFFFFF;
  position: absolute;
  top: 0;
  left: auto;
  transition: transform .3s;
  overflow: hidden;
  right: 0;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: transform 0.3s 0s ease-out;
  -moz-transition: transform 0.3s 0s ease-out;
  -o-transition: transform 0.3s 0s ease-out;
  transition: transform 0.3s 0s ease-out;
}
.button .ball:first-child {
  left: 0;
  right: auto;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -o-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  transform-origin: left center;
}
.button .ball:first-child i:first-child {
  -webkit-transform: translate(-150%, 50%) scale(0.5);
  -moz-transform: translate(-150%, 50%) scale(0.5);
  -o-transform: translate(-150%, 50%) scale(0.5);
  -ms-transform: translate(-150%, 50%) scale(0.5);
  transform: translate(-150%, 50%) scale(0.5);
}
.button .ball:last-child {
  transform-origin: left center;
}
.button .ball i {
  position: absolute;
  left: 50%;
  top: 50%;
  font-size: 1.505vw;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -moz-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  -o-transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
  transition: transform 0.6s 0s cubic-bezier(0.85, 0, 0.15, 1);
}
.goatHomeHeaderBlock {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding-top: 0 !important;
  z-index: 2;
}
.goatHomeHeaderBlock.inview .svgBackground {
  opacity: 0.4;
  -webkit-transition: opacity 0.6s 0.9s ease-in-out;
  -moz-transition: opacity 0.6s 0.9s ease-in-out;
  -o-transition: opacity 0.6s 0.9s ease-in-out;
  transition: opacity 0.6s 0.9s ease-in-out;
}
.goatHomeHeaderBlock .headerBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.goatHomeHeaderBlock .headerBackground.sticky {
  position: fixed;
}
.goatHomeHeaderBlock .headerBackground .bgImage,
.goatHomeHeaderBlock .headerBackground .bgVideo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.goatHomeHeaderBlock .headerBackground::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}
.goatHomeHeaderBlock .svgBackground {
  position: absolute;
  opacity: 0;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}
.goatHomeHeaderBlock .svgBackground svg {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
}
.goatHomeHeaderBlock .svgBackground svg.animate path {
  animation-name: shimmer;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  transform-origin: center;
  -webkit-transition: opacity 0.3s 0s ease-out;
  -moz-transition: opacity 0.3s 0s ease-out;
  -o-transition: opacity 0.3s 0s ease-out;
  transition: opacity 0.3s 0s ease-out;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(50) {
  animation-delay: 5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(49) {
  animation-delay: 4.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(48) {
  animation-delay: 4.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(47) {
  animation-delay: 4.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(46) {
  animation-delay: 4.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(45) {
  animation-delay: 4.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(44) {
  animation-delay: 4.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(43) {
  animation-delay: 4.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(42) {
  animation-delay: 4.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(41) {
  animation-delay: 4.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(40) {
  animation-delay: 4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(39) {
  animation-delay: 3.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(38) {
  animation-delay: 3.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(37) {
  animation-delay: 3.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(36) {
  animation-delay: 3.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(35) {
  animation-delay: 3.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(34) {
  animation-delay: 3.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(33) {
  animation-delay: 3.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(32) {
  animation-delay: 3.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(31) {
  animation-delay: 3.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(30) {
  animation-delay: 3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(29) {
  animation-delay: 2.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(28) {
  animation-delay: 2.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(27) {
  animation-delay: 2.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(26) {
  animation-delay: 2.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(25) {
  animation-delay: 2.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(24) {
  animation-delay: 2.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(23) {
  animation-delay: 2.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(22) {
  animation-delay: 2.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(21) {
  animation-delay: 2.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(20) {
  animation-delay: 2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(19) {
  animation-delay: 1.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(18) {
  animation-delay: 1.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(17) {
  animation-delay: 1.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(16) {
  animation-delay: 1.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(15) {
  animation-delay: 1.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(14) {
  animation-delay: 1.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(13) {
  animation-delay: 1.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(12) {
  animation-delay: 1.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(11) {
  animation-delay: 1.1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(10) {
  animation-delay: 1s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(9) {
  animation-delay: 0.9s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(8) {
  animation-delay: 0.8s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(7) {
  animation-delay: 0.7s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(6) {
  animation-delay: 0.6s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(5) {
  animation-delay: 0.5s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(4) {
  animation-delay: 0.4s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(3) {
  animation-delay: 0.3s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(2) {
  animation-delay: 0.2s;
}
.goatHomeHeaderBlock .svgBackground svg.animate path:nth-child(1) {
  animation-delay: 0.1s;
}
.goatHomeHeaderBlock .contentWrapper {
  position: absolute;
  z-index: 3;
  text-align: left;
  bottom: 1.157vw;
  color: white;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper {
  margin-bottom: 2rem;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper .mainLogo {
  max-width: 200px;
  height: auto;
}
@media (max-width: 768px) {
  .goatHomeHeaderBlock .contentWrapper .headerContent .logoWrapper .mainLogo {
    max-width: 150px;
  }
}
.goatHomeHeaderBlock .contentWrapper .headerContent .subTitle {
  margin-bottom: 1.157vw;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper {
  margin-bottom: 3rem;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
}
.goatHomeHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  color: white;
  opacity: 0.7;
  animation: bounce 2s infinite;
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator .scrollText {
  font-size: 0.9rem;
  margin-bottom: 10px;
}
.goatHomeHeaderBlock .contentWrapper .scrollIndicator .scrollArrow {
  font-size: 1.2rem;
}
@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}
@keyframes shimmer {
  0%,
  100% {
    opacity: 0.5;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
  }
}
.goatHomeIntroBlock .contentWrapper .introContent {
  position: relative;
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage {
  position: absolute;
  top: 50%;
  left: 25%;
  width: 30.0924vw;
  perspective: 200px;
  height: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage .innerImage {
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  position: relative;
  transform-style: preserve-3d;
}
.goatHomeIntroBlock .contentWrapper .introContent .sideImage img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  object-fit: contain;
  object-position: center;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper {
  margin-left: auto;
  position: relative;
  width: calc(50% - 0.926vw);
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle {
  position: relative;
  opacity: 0.2;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle.overlayText {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 1;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .mediumTitle.overlayText .line {
  white-space: nowrap;
}
.goatHomeIntroBlock .contentWrapper .introTextWrapper .signatureTitle {
  margin: 0.289vw 0;
  display: block;
  width: 100%;
  text-align: right;
}
.goatTeamMembersHomeBlock {
  padding: 100px 0;
  background: #1a1a1a;
  color: white;
}
.goatTeamMembersHomeBlock .contentWrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent {
  display: flex;
  align-items: flex-start;
  gap: 80px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent {
    flex-direction: column;
    gap: 60px;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent {
  flex: 2;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader {
  margin-bottom: 60px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle {
  font-size: 4rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.1;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle .highlight {
  color: #D4AF37;
  font-style: italic;
}
@media (max-width: 768px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .sectionHeader .mainTitle {
    font-size: 2.5rem;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}
@media (max-width: 768px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberLink {
  text-decoration: none;
  color: inherit;
  display: block;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage {
  position: relative;
  overflow: hidden;
  height: 320px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberImage:hover img {
  transform: scale(1.05);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo {
  padding: 25px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberName {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials {
  display: flex;
  gap: 12px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink:hover {
  background: #D4AF37;
  color: #1a1a1a;
  transform: translateY(-3px);
  border-color: #D4AF37;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .leftContent .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink i {
  font-size: 18px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent {
  flex: 1;
  padding-top: 120px;
}
@media (max-width: 1024px) {
  .goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent {
    padding-top: 0;
  }
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 40px;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button {
  background: transparent;
  color: white;
  padding: 15px 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 15px;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button:hover {
  background: #D4AF37;
  border-color: #D4AF37;
  color: #1a1a1a;
  transform: translateY(-2px);
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
  font-size: 1.2rem;
}
.goatTeamMembersHomeBlock .contentWrapper .sectionContent .rightContent .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
.goatBigImageSliderBlock {
  position: relative;
  height: 70vh;
  min-height: 500px;
  overflow: hidden;
}
@media (max-width: 768px) {
  .goatBigImageSliderBlock {
    height: 50vh;
    min-height: 400px;
  }
}
.goatBigImageSliderBlock .bigImageSlider {
  position: relative;
  width: 100%;
  height: 100%;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.6s ease-in-out;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide {
  flex: 0 0 100%;
  position: relative;
  height: 100%;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideImage {
  width: 100%;
  height: 100%;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper {
  max-width: 800px;
  padding: 0 20px;
  text-align: center;
  color: white;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideTitle {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}
@media (max-width: 768px) {
  .goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideTitle {
    font-size: 2rem;
  }
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideDescription {
  font-size: 1.2rem;
  margin-bottom: 30px;
  line-height: 1.6;
  opacity: 0.9;
}
@media (max-width: 768px) {
  .goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideDescription {
    font-size: 1rem;
  }
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideButton .textLink {
  color: white;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 12px 25px;
  border: 2px solid white;
  border-radius: 50px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideButton .textLink:hover {
  background: white;
  color: #333;
  transform: translateY(-2px);
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideButton .textLink .arrows {
  transition: transform 0.3s ease;
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide .slideContent .contentWrapper .slideButton .textLink:hover .arrows {
  transform: translateX(5px);
}
.goatBigImageSliderBlock .bigImageSlider .sliderWrapper .slide.active .slideContent .contentWrapper {
  animation: slideContentIn 0.8s ease forwards;
}
.goatBigImageSliderBlock .bigImageSlider .sliderControls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}
.goatBigImageSliderBlock .bigImageSlider .sliderControls .sliderArrow {
  pointer-events: all;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}
.goatBigImageSliderBlock .bigImageSlider .sliderControls .sliderArrow:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}
.goatBigImageSliderBlock .bigImageSlider .sliderControls .sliderArrow:active {
  transform: scale(0.95);
}
@media (max-width: 768px) {
  .goatBigImageSliderBlock .bigImageSlider .sliderControls .sliderArrow {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}
.goatBigImageSliderBlock .bigImageSlider .sliderDots {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}
.goatBigImageSliderBlock .bigImageSlider .sliderDots .dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}
.goatBigImageSliderBlock .bigImageSlider .sliderDots .dot.active {
  background: white;
  transform: scale(1.2);
}
.goatBigImageSliderBlock .bigImageSlider .sliderDots .dot:hover {
  background: rgba(255, 255, 255, 0.8);
}
@keyframes slideContentIn {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.goatPartnersTeamEventsBlock {
  padding: 80px 0;
  background: #f8f9fa;
}
.goatPartnersTeamEventsBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatPartnersTeamEventsBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatPartnersTeamEventsBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatPartnersTeamEventsBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatPartnersTeamEventsBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 60px;
}
@media (min-width: 1024px) {
  .goatPartnersTeamEventsBlock .contentWrapper .combinedGrid {
    grid-template-columns: 1fr 1fr;
    gap: 80px;
  }
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .sectionTitle,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .sectionTitle,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .sectionTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .sectionTitle::after,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .sectionTitle::after,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-radius: 2px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection {
  grid-column: -1;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard {
  background: white;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard .partnerLogo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard .partnerLogo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard .partnerName h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard:hover .partnerLogo img {
  filter: grayscale(0%);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .partnersSection .partnersGrid .partnerCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberImage {
  height: 150px;
  overflow: hidden;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberImage:hover img {
  transform: scale(1.05);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberInfo {
  padding: 15px;
  text-align: center;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberInfo .memberName {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard .memberInfo .memberRole {
  color: #ff6b35;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .teamSection .teamGrid .teamMemberCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventImage {
  height: 120px;
  overflow: hidden;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventImage:hover img {
  transform: scale(1.05);
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo {
  padding: 15px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo .eventTitle {
  font-size: 1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo .eventDate,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo .eventLocation {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo .eventDate i,
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard .eventInfo .eventLocation i {
  color: #ff6b35;
}
.goatPartnersTeamEventsBlock .contentWrapper .combinedGrid .eventsSection .eventsGrid .eventCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.goatInstagramEmbedBlock {
  padding: 80px 0;
  background: white;
}
.goatInstagramEmbedBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatInstagramEmbedBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatInstagramEmbedBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatInstagramEmbedBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatInstagramEmbedBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatInstagramEmbedBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent {
  margin-bottom: 50px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost {
  position: relative;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .postImage {
  aspect-ratio: 1;
  overflow: hidden;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .postImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .postCaption {
  padding: 15px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .postCaption p {
  color: #333;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .instagramIcon {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0.8;
  transition: all 0.3s ease;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost .instagramIcon i {
  font-size: 14px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost:hover .postImage img {
  transform: scale(1.05);
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost:hover .instagramIcon {
  opacity: 1;
  transform: scale(1.1);
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramGrid .instagramPost.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramPlaceholder {
  text-align: center;
  padding: 60px 20px;
  background: #f8f9fa;
  border-radius: 15px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramPlaceholder .instagram-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramPlaceholder p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 10px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramContent .instagramPlaceholder p small {
  font-size: 0.9rem;
  opacity: 0.7;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA {
  text-align: center;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton {
  background: linear-gradient(45deg, #E4405F, #C13584);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(228, 64, 95, 0.3);
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton .innerText {
  display: flex;
  align-items: center;
  gap: 8px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton .innerText i {
  font-size: 18px;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton .arrows {
  transition: transform 0.3s ease;
}
.goatInstagramEmbedBlock .contentWrapper .instagramCTA .instagramButton:hover .arrows {
  transform: translateX(5px);
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.goatHeaderBlock {
  position: relative;
  min-height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #333;
}
.goatHeaderBlock .headerBackground {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.goatHeaderBlock .headerBackground .bgImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.goatHeaderBlock .headerBackground .bgOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  opacity: 0.7;
}
.goatHeaderBlock .contentWrapper {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}
.goatHeaderBlock .contentWrapper .headerContent .breadcrumbs {
  margin-bottom: 20px;
  font-size: 0.9rem;
  opacity: 0.8;
}
.goatHeaderBlock .contentWrapper .headerContent .breadcrumbs a {
  color: white;
  text-decoration: none;
}
.goatHeaderBlock .contentWrapper .headerContent .breadcrumbs a:hover {
  text-decoration: underline;
}
.goatHeaderBlock .contentWrapper .headerContent .breadcrumbs .separator {
  margin: 0 10px;
}
.goatHeaderBlock .contentWrapper .headerContent .breadcrumbs .current {
  opacity: 0.6;
}
.goatHeaderBlock .contentWrapper .headerContent .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatHeaderBlock .contentWrapper .headerContent .pageTitle {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}
@media (max-width: 768px) {
  .goatHeaderBlock .contentWrapper .headerContent .pageTitle {
    font-size: 2rem;
  }
}
.goatHeaderBlock .contentWrapper .headerContent .description {
  font-size: 1.1rem;
  margin-bottom: 30px;
  line-height: 1.6;
  opacity: 0.9;
}
@media (max-width: 768px) {
  .goatHeaderBlock .contentWrapper .headerContent .description {
    font-size: 1rem;
  }
}
.goatHeaderBlock .contentWrapper .headerContent .ctaWrapper .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatHeaderBlock .contentWrapper .headerContent .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
}
.goatHeaderBlock .contentWrapper .headerContent .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
.goatHeaderBlock .contentWrapper .headerContent.animate-in {
  animation: fadeInUp 0.8s ease forwards;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.goatPartnersBlock {
  padding: 80px 0;
  background: white;
}
.goatPartnersBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatPartnersBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatPartnersBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatPartnersBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatPartnersBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatPartnersBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatPartnersBlock .contentWrapper .partnersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}
@media (max-width: 768px) {
  .goatPartnersBlock .contentWrapper .partnersGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  transition: all 0.3s ease;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: #ff6b35;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerLink {
  text-decoration: none;
  color: inherit;
  display: block;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerLogo {
  margin-bottom: 20px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerLogo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerLogo:hover img {
  filter: grayscale(0%);
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerInfo .partnerName {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerInfo .partnerDescription {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerInfo .partnerWebsite {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  color: #ff6b35;
  font-size: 0.9rem;
  font-weight: 600;
}
.goatPartnersBlock .contentWrapper .partnersGrid .partnerCard .partnerInfo .partnerWebsite i {
  font-size: 12px;
}
.goatPartnersBlock .contentWrapper .becomePartnerSection {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 20px;
  padding: 50px;
  text-align: center;
  color: white;
}
@media (max-width: 768px) {
  .goatPartnersBlock .contentWrapper .becomePartnerSection {
    padding: 30px 20px;
  }
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerTitle {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerTitle {
    font-size: 1.5rem;
  }
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerDescription {
  font-size: 1.1rem;
  margin-bottom: 30px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerCTA .button {
  background: white;
  color: #ff6b35;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerCTA .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerCTA .button .arrows {
  transition: transform 0.3s ease;
}
.goatPartnersBlock .contentWrapper .becomePartnerSection .becomePartnerContent .becomePartnerCTA .button:hover .arrows {
  transform: translateX(5px);
}
.goatThreeEventsBlock {
  padding: 80px 0;
  background: white;
}
.goatThreeEventsBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatThreeEventsBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatThreeEventsBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatThreeEventsBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatThreeEventsBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatThreeEventsBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}
@media (max-width: 768px) {
  .goatThreeEventsBlock .contentWrapper .eventsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .placeholderImage {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 2rem;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventDateBadge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(255, 107, 53, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventDateBadge .day {
  display: block;
  font-size: 1.2rem;
  line-height: 1;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventDateBadge .month {
  display: block;
  font-size: 0.8rem;
  text-transform: uppercase;
  opacity: 0.9;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventDateBadge .date {
  font-size: 0.9rem;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventDateBadge.upcoming {
  animation: pulse 2s infinite;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo {
  padding: 25px;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventTitle {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.3;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventLocation {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  margin-bottom: 15px;
  font-size: 0.9rem;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventLocation i {
  color: #ff6b35;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventDescription {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
  font-size: 0.95rem;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventDate {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #666;
  font-size: 0.9rem;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventDate i {
  color: #ff6b35;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .readMore {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff6b35;
  font-weight: 600;
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .readMore i {
  font-size: 12px;
}
.goatThreeEventsBlock .contentWrapper .eventsGrid .eventCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatThreeEventsBlock .contentWrapper .ctaWrapper {
  text-align: center;
}
.goatThreeEventsBlock .contentWrapper .ctaWrapper .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .ctaWrapper .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatThreeEventsBlock .contentWrapper .ctaWrapper .button .arrows {
  transition: transform 0.3s ease;
}
.goatThreeEventsBlock .contentWrapper .ctaWrapper .button:hover .arrows {
  transform: translateX(5px);
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
.goatEventsBlock {
  padding: 80px 0;
  background: #f8f9fa;
}
.goatEventsBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatEventsBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 40px;
}
.goatEventsBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatEventsBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatEventsBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatEventsBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatEventsBlock .contentWrapper .eventFilters {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}
.goatEventsBlock .contentWrapper .eventFilters .filterBtn {
  padding: 10px 20px;
  border: 2px solid #e9ecef;
  background: white;
  color: #666;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}
.goatEventsBlock .contentWrapper .eventFilters .filterBtn:hover {
  border-color: #ff6b35;
  color: #ff6b35;
}
.goatEventsBlock .contentWrapper .eventFilters .filterBtn.active {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-color: #ff6b35;
  color: white;
}
.goatEventsBlock .contentWrapper .eventsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard.fade-in {
  animation: fadeIn 0.5s ease forwards;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage {
  position: relative;
  height: 200px;
  overflow: hidden;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .placeholderImage {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 2rem;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventStatus {
  position: absolute;
  top: 15px;
  right: 15px;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventStatus .statusBadge {
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventStatus .statusBadge.upcoming {
  background: rgba(40, 167, 69, 0.9);
  color: white;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventImage .eventStatus .statusBadge.past {
  background: rgba(108, 117, 125, 0.9);
  color: white;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo {
  padding: 25px;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventTitle {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  line-height: 1.3;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta {
  margin-bottom: 15px;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventDate,
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventLocation {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  margin-bottom: 8px;
  font-size: 0.9rem;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventDate i,
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventMeta .eventLocation i {
  color: #ff6b35;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventDescription {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
  font-size: 0.95rem;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventFooter .readMore {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff6b35;
  font-weight: 600;
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventFooter .readMore i {
  font-size: 12px;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard .eventInfo .eventFooter .quickRegister .registerText {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}
.goatEventsBlock .contentWrapper .eventsGrid .eventCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper {
  text-align: center;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn.loading .arrows {
  animation: spin 1s linear infinite;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn .arrows {
  transition: transform 0.3s ease;
}
.goatEventsBlock .contentWrapper .loadMoreWrapper .loadMoreBtn:hover:not(.loading) .arrows {
  transform: translateY(3px);
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.goatTextTwoColumnBlock {
  padding: 80px 0;
  background: white;
}
.goatTextTwoColumnBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatTextTwoColumnBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatTextTwoColumnBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatTextTwoColumnBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatTextTwoColumnBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatTextTwoColumnBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 60px;
}
@media (max-width: 768px) {
  .goatTextTwoColumnBlock .contentWrapper .twoColumnContent {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnTitle,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent {
  color: #666;
  line-height: 1.6;
  margin-bottom: 25px;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent p,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent p {
  margin-bottom: 15px;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent ul,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent ul,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent ol,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent ol {
  padding-left: 20px;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent ul li,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent ul li,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnContent ol li,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnContent ol li {
  margin-bottom: 8px;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnImage,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnImage {
  margin-bottom: 25px;
  border-radius: 10px;
  overflow: hidden;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnImage img,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnImage img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnImage img:hover,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnImage img:hover {
  transform: scale(1.02);
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnButton .button,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnButton .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 12px 25px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnButton .button:hover,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnButton .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(255, 107, 53, 0.3);
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnButton .button .arrows,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnButton .button .arrows {
  transition: transform 0.3s ease;
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn .columnButton .button:hover .arrows,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn .columnButton .button:hover .arrows {
  transform: translateX(3px);
}
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .leftColumn.animate-in,
.goatTextTwoColumnBlock .contentWrapper .twoColumnContent .rightColumn.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA {
  text-align: center;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 20px;
  padding: 50px;
  color: white;
}
@media (max-width: 768px) {
  .goatTextTwoColumnBlock .contentWrapper .bottomCTA {
    padding: 30px 20px;
  }
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaTitle {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaTitle {
    font-size: 1.5rem;
  }
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaDescription {
  font-size: 1.1rem;
  margin-bottom: 30px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaButton .button {
  background: white;
  color: #ff6b35;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaButton .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaButton .button .arrows {
  transition: transform 0.3s ease;
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA .ctaButton .button:hover .arrows {
  transform: translateX(5px);
}
.goatTextTwoColumnBlock .contentWrapper .bottomCTA.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.goatStepsBlock {
  padding: 80px 0;
  background: #f8f9fa;
}
.goatStepsBlock .contentWrapper {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatStepsBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 60px;
}
.goatStepsBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatStepsBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatStepsBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatStepsBlock .contentWrapper .stepsContainer {
  margin-bottom: 60px;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 30px;
  align-items: flex-start;
  margin-bottom: 40px;
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .stepsContainer .stepItem {
    grid-template-columns: auto 1fr;
    gap: 20px;
  }
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem:last-child {
  margin-bottom: 0;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem:last-child .stepConnector {
  display: none;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepNumber {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: white;
  border: 3px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: #666;
  transition: all 0.3s ease;
  flex-shrink: 0;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepNumber {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent {
    padding: 20px;
  }
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepIcon {
  margin-bottom: 20px;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepIcon img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  transition: transform 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepTitle {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepDescription {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepImage {
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepImage img {
  width: 100%;
  height: auto;
  transition: transform 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepButton .textLink {
  color: #ff6b35;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepButton .textLink:hover {
  color: #f7931e;
  transform: translateX(3px);
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepButton .textLink .arrows {
  transition: transform 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepContent .stepButton .textLink:hover .arrows {
  transform: translateX(3px);
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100px;
  margin-top: 30px;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector {
    display: none;
  }
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector .connectorLine {
  width: 2px;
  height: 60px;
  background: #e9ecef;
  transition: background 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector .connectorArrow {
  color: #e9ecef;
  font-size: 1.2rem;
  margin-top: 10px;
  transition: color 0.3s ease;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector.animate-connector .connectorLine {
  background: linear-gradient(to bottom, #ff6b35, #f7931e);
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem .stepConnector.animate-connector .connectorArrow {
  color: #ff6b35;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem:hover .stepNumber {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-color: #ff6b35;
  color: white;
  transform: scale(1.1);
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem:hover .stepContent {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem.active .stepNumber {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-color: #ff6b35;
  color: white;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem.active .stepContent {
  border-left: 4px solid #ff6b35;
}
.goatStepsBlock .contentWrapper .stepsContainer .stepItem.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatStepsBlock .contentWrapper .bottomCTA {
  text-align: center;
  background: white;
  border-radius: 20px;
  padding: 50px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .bottomCTA {
    padding: 30px 20px;
  }
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaTitle {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatStepsBlock .contentWrapper .bottomCTA .ctaTitle {
    font-size: 1.5rem;
  }
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaDescription {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaButton .button {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaButton .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaButton .button .arrows {
  transition: transform 0.3s ease;
}
.goatStepsBlock .contentWrapper .bottomCTA .ctaButton .button:hover .arrows {
  transform: translateX(5px);
}
.goatStepsBlock .contentWrapper .bottomCTA.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.goatContactBlock {
  padding: 80px 0;
  background: white;
}
.goatContactBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatContactBlock .contentWrapper .contactContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}
@media (max-width: 968px) {
  .goatContactBlock .contentWrapper .contactContent {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .sectionHeader {
  margin-bottom: 40px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatContactBlock .contentWrapper .contactContent .contactInfo .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails {
  margin-bottom: 40px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 25px;
  transition: transform 0.3s ease;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactIcon {
  width: 50px;
  height: 50px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactIcon i {
  font-size: 20px;
  color: #666;
  transition: all 0.3s ease;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactText h4 {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactText a {
  color: #ff6b35;
  text-decoration: none;
  font-weight: 600;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactText a:hover {
  text-decoration: underline;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem .contactText p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem:hover {
  transform: translateX(5px);
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem:hover .contactIcon {
  background: #ff6b35;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem:hover .contactIcon i {
  color: white;
  transform: scale(1.1);
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .contactDetails .contactItem.animate-in {
  animation: fadeInLeft 0.6s ease forwards;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia h4 {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia .socialLinks {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia .socialLinks .socialLink {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 25px;
  color: #666;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia .socialLinks .socialLink i {
  font-size: 16px;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia .socialLinks .socialLink:hover {
  background: #ff6b35;
  color: white;
  transform: translateY(-2px);
}
.goatContactBlock .contentWrapper .contactContent .contactInfo .socialMedia .socialLinks .socialLink.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatContactBlock .contentWrapper .contactContent .contactInfo.animate-in {
  animation: fadeInLeft 0.8s ease forwards;
}
.goatContactBlock .contentWrapper .contactContent .contactForm {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 20px;
}
@media (max-width: 768px) {
  .goatContactBlock .contentWrapper .contactContent .contactForm {
    padding: 30px 20px;
  }
}
.goatContactBlock .contentWrapper .contactContent .contactForm .formTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup {
  position: relative;
  margin-bottom: 25px;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup label {
  position: absolute;
  top: 15px;
  left: 15px;
  color: #666;
  font-weight: 600;
  transition: all 0.3s ease;
  pointer-events: none;
  background: white;
  padding: 0 5px;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup input,
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup textarea {
  width: 100%;
  padding: 15px;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup input:focus,
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup textarea:focus {
  outline: none;
  border-color: #ff6b35;
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup textarea {
  resize: vertical;
  min-height: 120px;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup.focused label,
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup:focus-within label {
  top: -8px;
  left: 10px;
  font-size: 0.9rem;
  color: #ff6b35;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup.error input,
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup.error textarea {
  border-color: #dc3545;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .formGroup.error .error-message {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 5px;
  font-weight: 600;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn {
  width: 100%;
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  color: white;
  padding: 15px 30px;
  border-radius: 50px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn.loading .arrows {
  animation: spin 1s linear infinite;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn .arrows {
  transition: transform 0.3s ease;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .submitBtn:hover:not(.loading) .arrows {
  transform: translateX(5px);
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .form-message {
  margin-top: 20px;
  padding: 15px;
  border-radius: 10px;
  font-weight: 600;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .form-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}
.goatContactBlock .contentWrapper .contactContent .contactForm .defaultContactForm .form-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
.goatContactBlock .contentWrapper .contactContent .contactForm.animate-in {
  animation: fadeInRight 0.8s ease 0.2s forwards;
  opacity: 0;
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.goatTeamOverviewBlock {
  padding: 80px 0;
  background: #f8f9fa;
}
.goatTeamOverviewBlock .contentWrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.goatTeamOverviewBlock .contentWrapper .sectionHeader {
  text-align: center;
  margin-bottom: 40px;
}
.goatTeamOverviewBlock .contentWrapper .sectionHeader .subTitle {
  color: #ff6b35;
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.goatTeamOverviewBlock .contentWrapper .sectionHeader .biggerTitle {
  font-size: 2.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatTeamOverviewBlock .contentWrapper .sectionHeader .biggerTitle {
    font-size: 2rem;
  }
}
.goatTeamOverviewBlock .contentWrapper .sectionHeader .description {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}
.goatTeamOverviewBlock .contentWrapper .teamFilters {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}
.goatTeamOverviewBlock .contentWrapper .teamFilters .filterBtn {
  padding: 10px 20px;
  border: 2px solid #e9ecef;
  background: white;
  color: #666;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .teamFilters .filterBtn:hover {
  border-color: #ff6b35;
  color: #ff6b35;
}
.goatTeamOverviewBlock .contentWrapper .teamFilters .filterBtn.active {
  background: linear-gradient(45deg, #ff6b35, #f7931e);
  border-color: #ff6b35;
  color: white;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}
@media (max-width: 768px) {
  .goatTeamOverviewBlock .contentWrapper .teamMembersGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberLink {
  text-decoration: none;
  color: inherit;
  display: block;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage {
  position: relative;
  height: 250px;
  overflow: hidden;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage .placeholderImage {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 3rem;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage .memberOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 107, 53, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage .memberOverlay .viewProfile {
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage .memberOverlay .viewProfile i {
  font-size: 16px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage:hover img {
  transform: scale(1.05);
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberImage:hover .memberOverlay {
  opacity: 1;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo {
  padding: 25px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberName {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberRole {
  color: #ff6b35;
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberBio {
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
  font-size: 0.95rem;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberSocials {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background: #f1f3f4;
  border-radius: 50%;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  transform: translateY(10px) scale(0.8);
  opacity: 0.7;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink:hover {
  background: #ff6b35;
  color: white;
  transform: translateY(-2px) scale(1.1);
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberSocials .socialLink i {
  font-size: 16px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberAchievements .achievement {
  background: #f8f9fa;
  padding: 5px 10px;
  border-radius: 15px;
  display: inline-block;
  margin: 2px;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard .memberInfo .memberAchievements .achievement .achievementTitle {
  font-size: 0.8rem;
  color: #666;
  font-weight: 600;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard.animate-in {
  animation: fadeInUp 0.6s ease forwards;
}
.goatTeamOverviewBlock .contentWrapper .teamMembersGrid .teamMemberCard.fade-in {
  animation: fadeIn 0.5s ease forwards;
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection {
  text-align: center;
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  border-radius: 20px;
  padding: 50px;
  color: white;
}
@media (max-width: 768px) {
  .goatTeamOverviewBlock .contentWrapper .joinTeamSection {
    padding: 30px 20px;
  }
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamTitle {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamTitle {
    font-size: 1.5rem;
  }
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamDescription {
  font-size: 1.1rem;
  margin-bottom: 30px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamCTA .button {
  background: white;
  color: #ff6b35;
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamCTA .button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamCTA .button .arrows {
  transition: transform 0.3s ease;
}
.goatTeamOverviewBlock .contentWrapper .joinTeamSection .joinTeamContent .joinTeamCTA .button:hover .arrows {
  transform: translateX(5px);
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@font-face {
  font-family: 'icomoon';
  src: url('assets/fonts/icomoon.eot?twq3hi');
  src: url('assets/fonts/icomoon.eot?twq3hi#iefix') format('embedded-opentype'), url('assets/fonts/icomoon.ttf?twq3hi') format('truetype'), url('assets/fonts/icomoon.woff?twq3hi') format('woff'), url('assets/fonts/icomoon.svg?twq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
[class^="icon-"],
[class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-arrow-right-up:before {
  content: "\e900";
}
.icon-arrow-right:before {
  content: "\e901";
}
.icon-arrow-left:before {
  content: "\e902";
}
.icon-arrow-right-down:before {
  content: "\e903";
}
.icon-arrow-down:before {
  content: "\e904";
}
.icon-arrow-up:before {
  content: "\e905";
}
.icon-insta:before {
  content: "\e906";
}
.icon-tiktok:before {
  content: "\e907";
}
.icon-phone:before {
  content: "\e908";
}
.icon-mail:before {
  content: "\e909";
}
::-webkit-scrollbar {
  width: 0.579vw;
}
::-webkit-scrollbar-track {
  background: #C2C2B5;
}
::-webkit-scrollbar-thumb {
  border-radius: 2.894vw;
  background: rgba(0, 0, 0, 0.1);
}
.block__headline {
  padding: 20px 15px 30px;
  background: #fafafa;
  text-align: center;
}
.block__headline-title {
  font-family: 'Arial', sans-serif;
  font-size: 30px;
  font-weight: bold;
  position: relative;
}
.block__headline-title:after {
  content: '';
  display: block;
  width: 40px;
  height: 2px;
  background: #333;
  margin: 0 auto;
}
html.has-scroll-smooth {
  backface-visibility: hidden;
  transform: translateZ(0);
}
html.has-scroll-smooth [data-load-container] {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
}
.transition-fade {
  transition: .75s;
  opacity: 1;
}
html.is-animating .transition-fade {
  opacity: 0;
}
.grecaptcha-badge {
  visibility: hidden;
}
@keyframes dash-move {
  from {
    stroke-dashoffset: 0;
    /* Startpositie */
  }
  to {
    stroke-dashoffset: -20;
    /* Naar links verschuiven */
  }
}
