<?php get_header(); ?>

<div class="team-member-detail">
    <div class="container">
        <?php if (have_posts()) : while (have_posts()) : the_post(); ?>
            
            <?php 
            // Check if detail page is enabled
            $show_detail_page = get_field('show_detail_page');
            if (!$show_detail_page) {
                // Redirect to team overview or 404
                wp_redirect(home_url('/team'));
                exit;
            }
            ?>
            
            <div class="team-member-header">
                <div class="member-image">
                    <?php if (has_post_thumbnail()) : ?>
                        <?php the_post_thumbnail('large'); ?>
                    <?php endif; ?>
                </div>
                
                <div class="member-info">
                    <h1 class="member-name"><?php the_title(); ?></h1>
                    
                    <?php if (get_field('member_role')) : ?>
                        <div class="member-role">
                            <span><?php echo get_field('member_role'); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (get_field('member_bio')) : ?>
                        <div class="member-bio">
                            <p><?php echo get_field('member_bio'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if (get_field('member_additional_images')) : ?>
                <div class="member-gallery">
                    <?php 
                    $images = get_field('member_additional_images');
                    if ($images) : ?>
                        <div class="image-grid">
                            <?php foreach ($images as $image) : ?>
                                <div class="image-item">
                                    <img src="<?php echo esc_url($image['sizes']['medium_large']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="member-content">
                <?php the_content(); ?>
            </div>

            <?php 
            // Social Media Links
            $socials = array(
                'twitch' => array('icon' => 'icon-twitch', 'label' => 'Twitch'),
                'tiktok' => array('icon' => 'icon-tiktok', 'label' => 'TikTok'),
                'instagram' => array('icon' => 'icon-instagram', 'label' => 'Instagram'),
                'twitter' => array('icon' => 'icon-twitter', 'label' => 'Twitter'),
                'discord' => array('icon' => 'icon-discord', 'label' => 'Discord'),
                'facebook' => array('icon' => 'icon-facebook', 'label' => 'Facebook'),
                'youtube' => array('icon' => 'icon-youtube', 'label' => 'YouTube')
            );
            
            $has_socials = false;
            foreach ($socials as $social => $data) {
                if (get_field('member_' . $social)) {
                    $has_socials = true;
                    break;
                }
            }
            ?>

            <?php if ($has_socials) : ?>
                <div class="member-socials">
                    <h3>Follow <?php the_title(); ?></h3>
                    <div class="social-links">
                        <?php foreach ($socials as $social => $data) : ?>
                            <?php if (get_field('member_' . $social)) : ?>
                                <a href="<?php echo esc_url(get_field('member_' . $social)); ?>" class="social-link" target="_blank" rel="noopener">
                                    <i class="<?php echo $data['icon']; ?>"></i>
                                    <span><?php echo $data['label']; ?></span>
                                </a>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

        <?php endwhile; endif; ?>
    </div>
</div>

<?php get_footer(); ?>
