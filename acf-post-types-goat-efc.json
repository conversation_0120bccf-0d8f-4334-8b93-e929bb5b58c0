[{"key": "post_type_partner", "title": "Partners", "description": "GOAT EFC Partners and Sponsors", "labels": {"singular_name": "Partner", "plural_name": "Partners", "menu_name": "Partners", "add_new": "Add New Partner", "add_new_item": "Add New Partner", "edit_item": "Edit Partner", "new_item": "New Partner", "view_item": "View Partner", "view_items": "View Partners", "search_items": "Search Partners", "not_found": "No partners found", "not_found_in_trash": "No partners found in trash", "parent_item_colon": "Parent Partner:", "all_items": "All Partners", "archives": "Partner Archives", "attributes": "Partner Attributes", "insert_into_item": "Insert into partner", "uploaded_to_this_item": "Uploaded to this partner", "featured_image": "Partner Logo", "set_featured_image": "Set partner logo", "remove_featured_image": "Remove partner logo", "use_featured_image": "Use as partner logo"}, "public": 1, "publicly_queryable": 1, "show_ui": 1, "show_in_menu": 1, "show_in_nav_menus": 1, "show_in_admin_bar": 1, "show_in_rest": 1, "rest_base": "partners", "rest_controller_class": "WP_REST_Posts_Controller", "rest_namespace": "wp/v2", "has_archive": 1, "has_archive_string": "partners", "exclude_from_search": 0, "capability_type": "post", "hierarchical": 0, "can_export": 1, "rewrite": {"slug": "partners", "with_front": 1, "feeds": 1, "pages": 1}, "query_var": "partner", "menu_position": 20, "menu_icon": "dashicons-groups", "supports": ["title", "editor", "thumbnail", "excerpt"], "taxonomies": [], "active": 1}, {"key": "post_type_event", "title": "Events", "description": "GOAT EFC Events and Tournaments", "labels": {"singular_name": "Event", "plural_name": "Events", "menu_name": "Events", "add_new": "Add New Event", "add_new_item": "Add New Event", "edit_item": "Edit Event", "new_item": "New Event", "view_item": "View Event", "view_items": "View Events", "search_items": "Search Events", "not_found": "No events found", "not_found_in_trash": "No events found in trash", "parent_item_colon": "Parent Event:", "all_items": "All Events", "archives": "Event Archives", "attributes": "Event Attributes", "insert_into_item": "Insert into event", "uploaded_to_this_item": "Uploaded to this event", "featured_image": "Event Image", "set_featured_image": "Set event image", "remove_featured_image": "Remove event image", "use_featured_image": "Use as event image"}, "public": 1, "publicly_queryable": 1, "show_ui": 1, "show_in_menu": 1, "show_in_nav_menus": 1, "show_in_admin_bar": 1, "show_in_rest": 1, "rest_base": "events", "rest_controller_class": "WP_REST_Posts_Controller", "rest_namespace": "wp/v2", "has_archive": 1, "has_archive_string": "events", "exclude_from_search": 0, "capability_type": "post", "hierarchical": 0, "can_export": 1, "rewrite": {"slug": "events", "with_front": 1, "feeds": 1, "pages": 1}, "query_var": "event", "menu_position": 21, "menu_icon": "dashicons-calendar-alt", "supports": ["title", "editor", "thumbnail", "excerpt"], "taxonomies": [], "active": 1}, {"key": "post_type_team_member", "title": "Team Members", "description": "GOAT EFC Team Members", "labels": {"singular_name": "Team Member", "plural_name": "Team Members", "menu_name": "Team Members", "add_new": "Add New Team Member", "add_new_item": "Add New Team Member", "edit_item": "Edit Team Member", "new_item": "New Team Member", "view_item": "View Team Member", "view_items": "View Team Members", "search_items": "Search Team Members", "not_found": "No team members found", "not_found_in_trash": "No team members found in trash", "parent_item_colon": "Parent Team Member:", "all_items": "All Team Members", "archives": "Team Member Archives", "attributes": "Team Member Attributes", "insert_into_item": "Insert into team member", "uploaded_to_this_item": "Uploaded to this team member", "featured_image": "Team Member Photo", "set_featured_image": "Set team member photo", "remove_featured_image": "Remove team member photo", "use_featured_image": "Use as team member photo"}, "public": 1, "publicly_queryable": 1, "show_ui": 1, "show_in_menu": 1, "show_in_nav_menus": 1, "show_in_admin_bar": 1, "show_in_rest": 1, "rest_base": "team-members", "rest_controller_class": "WP_REST_Posts_Controller", "rest_namespace": "wp/v2", "has_archive": 1, "has_archive_string": "team", "exclude_from_search": 0, "capability_type": "post", "hierarchical": 0, "can_export": 1, "rewrite": {"slug": "team", "with_front": 1, "feeds": 1, "pages": 1}, "query_var": "team_member", "menu_position": 22, "menu_icon": "dashicons-admin-users", "supports": ["title", "editor", "thumbnail", "excerpt"], "taxonomies": [], "active": 1}]