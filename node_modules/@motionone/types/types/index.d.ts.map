{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAA;AAE3C,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAA;AAE3C,MAAM,WAAW,uBAAuB;IACtC,IAAI,EAAE,OAAO,CAAA;IACb,gBAAgB,EAAE,OAAO,CAAA;IACzB,OAAO,EAAE,MAAM,CAAA;IACf,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,MAAM,gBAAgB,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,IAAI,CAAA;AAElD,MAAM,MAAM,yBAAyB,CAAC,OAAO,IAAI,CAC/C,OAAO,EAAE,OAAO,KACb,kBAAkB,CAAA;AAEvB,MAAM,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,uBAAuB,CAAA;AAEvE,MAAM,MAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;AAExE,MAAM,MAAM,SAAS,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,GAAG,UAAU,CAAA;AAElE,MAAM,WAAW,sBAAsB;IACrC;;;;;;OAMG;IACH,IAAI,EAAE,YAAY,CAAA;IAElB;;;;;;OAMG;IACH,KAAK,EAAE,YAAY,CAAA;IACnB,YAAY,EAAE,YAAY,CAAA;IAE1B;;;;;;OAMG;IACH,MAAM,EAAE,YAAY,CAAA;IACpB,IAAI,CAAC,EAAE,YAAY,CAAA;IACnB,SAAS,EAAE,SAAS,CAAA;IACpB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;IAEtB;;OAEG;IACH,SAAS,EAAE,MAAM,GAAG,IAAI,CAAA;IAExB;;;;;;;OAOG;IACH,WAAW,EAAE,MAAM,GAAG,IAAI,CAAA;CAC3B;AAED;;;;;;;GAOG;AACH,MAAM,WAAW,iBAAkB,SAAQ,sBAAsB;IAC/D;;;;;;OAMG;IACH,IAAI,EAAE,YAAY,CAAA;IAElB;;;;;;OAMG;IACH,MAAM,EAAE,YAAY,CAAA;IAEpB;;;;;;;;;;OAUG;IACH,OAAO,EAAE,YAAY,CAAA;IAErB;;;;;;OAMG;IACH,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;IAEtB;;;;;;OAMG;IACH,QAAQ,EAAE,MAAM,CAAA;IAEhB;;;;;;;;;;OAUG;IACH,YAAY,EAAE,MAAM,CAAA;IAEpB;;OAEG;IACH,SAAS,EAAE,kBAAkB,CAAA;CAC9B;AAED,MAAM,MAAM,uBAAuB,GAAG;IACpC,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,CAAA;IAClC,QAAQ,CAAC,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,CAAA;AAE3C,MAAM,MAAM,uBAAuB,GAAG,aAAa,GAAG,IAAI,CAAA;AAE1D,MAAM,MAAM,MAAM,GACd,QAAQ,GACR,MAAM,GACN,SAAS,GACT,UAAU,GACV,aAAa,GACb,YAAY,GACZ,UAAU,GACV,SAAS,MAAM,KAAK,OAAO,GAAG,KAAK,GAAG,GACtC,gBAAgB,CAAA;AAEpB,MAAM,MAAM,eAAe,GAAG;IAC5B,eAAe,EAAE,CACf,SAAS,EAAE,uBAAuB,EAAE,EACpC,cAAc,CAAC,EAAE,OAAO,EACxB,mBAAmB,CAAC,EAAE,MAAM,MAAM,GAAG,MAAM,EAC3C,IAAI,CAAC,EAAE,MAAM,EACb,KAAK,CAAC,EAAE,WAAW,KAChB,uBAAuB,CAAA;CAC7B,CAAA;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IAEjB;;;;;;;;;;;OAWG;IACH,MAAM,CAAC,EAAE,eAAe,GAAG,MAAM,GAAG,MAAM,EAAE,GAAG,cAAc,CAAA;IAE7D;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,EAAE,CAAA;CAClB,CAAA;AAED,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,CAAA;AAE/D,MAAM,MAAM,eAAe,GAAG;IAC5B;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA;IACvC;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB;;;;OAIG;IACH,MAAM,CAAC,EAAE,MAAM,CAAA;IACf;;;;OAIG;IACH,SAAS,CAAC,EAAE,iBAAiB,CAAA;IAC7B;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAA;IACjB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAA;CACnB,CAAA;AAED,MAAM,MAAM,eAAe,GAAG;IAC5B,MAAM,CAAC,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,MAAM,MAAM,gBAAgB,GAAG,eAAe,GAC5C,eAAe,GACf,eAAe,GAAG;IAChB;;;;;;;OAOG;IACH,uBAAuB,CAAC,EAAE,OAAO,CAAA;CAClC,CAAA;AAEH,MAAM,WAAW,QAAQ;IACvB,MAAM,EAAE,CACN,OAAO,EAAE,WAAW,EACpB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,GAAG,EACd,OAAO,EAAE,gBAAgB,KACtB,IAAI,CAAA;IACT,WAAW,EAAE,OAAO,CAAA;CACrB;AAED,MAAM,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,CAAA"}