'use strict';

var mix = require('./mix.cjs.js');
var progress = require('./progress.cjs.js');

function fillOffset(offset, remaining) {
    const min = offset[offset.length - 1];
    for (let i = 1; i <= remaining; i++) {
        const offsetProgress = progress.progress(0, remaining, i);
        offset.push(mix.mix(min, 1, offsetProgress));
    }
}
function defaultOffset(length) {
    const offset = [0];
    fillOffset(offset, length - 1);
    return offset;
}

exports.defaultOffset = defaultOffset;
exports.fillOffset = fillOffset;
