{"version": 3, "file": "edge.js", "sourceRoot": "", "sources": ["../../../../src/gestures/scroll/offsets/edge.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAGrD,MAAM,CAAC,MAAM,UAAU,GAA+B;IACpD,KAAK,EAAE,CAAC;IACR,MAAM,EAAE,GAAG;IACX,GAAG,EAAE,CAAC;CACP,CAAA;AAED,MAAM,UAAU,WAAW,CAAC,IAAU,EAAE,MAAc,EAAE,KAAK,GAAG,CAAC;IAC/D,IAAI,KAAK,GAAG,CAAC,CAAA;IAEb;;;OAGG;IACH,IAAI,UAAU,CAAC,IAA+B,CAAC,KAAK,SAAS,EAAE,CAAC;QAC9D,IAAI,GAAG,UAAU,CAAC,IAA+B,CAAC,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnB,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC,CAAA;QAEjC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,KAAK,GAAG,QAAQ,CAAA;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC9B,IAAI,GAAG,QAAQ,GAAG,GAAG,CAAA;QACvB,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAA;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,KAAK,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAA;QAClE,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,QAAQ,CAAA;QACjB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnB,KAAK,GAAG,MAAM,GAAG,IAAI,CAAA;IACvB,CAAC;IAED,OAAO,KAAK,GAAG,KAAK,CAAA;AACtB,CAAC"}