{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/gestures/scroll/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAA;AACxC,OAAO,EAAE,gBAAgB,EAAE,MAAM,QAAQ,CAAA;AACzC,OAAO,EAAE,qBAAqB,EAAE,MAAM,qBAAqB,CAAA;AAG3D,MAAM,eAAe,GAAG,IAAI,OAAO,EAAyB,CAAA;AAC5D,MAAM,eAAe,GAAG,IAAI,OAAO,EAAyB,CAAA;AAC5D,MAAM,gBAAgB,GAAG,IAAI,OAAO,EAAiC,CAAA;AAIrE,MAAM,cAAc,GAAG,CAAC,OAAoB,EAAE,EAAE,CAC9C,OAAO,KAAK,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAA;AAUzD,MAAM,UAAU,MAAM,CACpB,QAAsC,EACtC,KAAsE,EAAE;QAAxE,EAAE,SAAS,GAAG,QAAQ,CAAC,eAAe,OAAkC,EAA7B,OAAO,cAAlD,aAAoD,CAAF;IAElD,IAAI,iBAAiB,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IAEvD;;;OAGG;IACH,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,GAAG,IAAI,GAAG,EAAE,CAAA;QAC7B,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,MAAM,IAAI,GAAG,gBAAgB,EAAE,CAAA;IAC/B,MAAM,gBAAgB,GAAG,qBAAqB,CAC5C,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,OAAO,CACR,CAAA;IACD,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;IAEvC;;;OAGG;IACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,GAAG,EAAE;YACpB,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,EAAE,CAAA;YAE9B,KAAK,MAAM,OAAO,IAAI,iBAAkB;gBAAE,OAAO,CAAC,OAAO,EAAE,CAAA;YAC3D,KAAK,MAAM,OAAO,IAAI,iBAAkB;gBAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC9D,KAAK,MAAM,OAAO,IAAI,iBAAkB;gBAAE,OAAO,CAAC,MAAM,EAAE,CAAA;QAC5D,CAAC,CAAA;QAED,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;QAExC,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC,CAAA;QACxC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;QAC9D,IAAI,SAAS,KAAK,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC3C,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAA;QAC7D,CAAC;QACD,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,SAAS,CAAE,CAAA;IAChD,MAAM,cAAc,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAA;IAEtD,OAAO,GAAG,EAAE;;QACV,IAAI,OAAO,QAAQ,KAAK,UAAU;YAAE,QAAQ,CAAC,IAAI,EAAE,CAAA;QAEnD,oBAAoB,CAAC,cAAc,CAAC,CAAA;QAEpC;;WAEG;QACH,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QACzD,IAAI,CAAC,iBAAiB;YAAE,OAAM;QAE9B,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAA;QAE1C,IAAI,iBAAiB,CAAC,IAAI;YAAE,OAAM;QAElC;;WAEG;QACH,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAC/C,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QAEjC,IAAI,QAAQ,EAAE,CAAC;YACb,cAAc,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;YACjE,MAAA,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC,2CAAI,CAAA;YAClC,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;QAChD,CAAC;IACH,CAAC,CAAA;AACH,CAAC"}