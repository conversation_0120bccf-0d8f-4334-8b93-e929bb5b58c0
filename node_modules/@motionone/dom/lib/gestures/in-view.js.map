{"version": 3, "file": "in-view.js", "sourceRoot": "", "sources": ["../../src/gestures/in-view.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAA;AAC3D,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAA;AAU7C,MAAM,UAAU,GAAG;IACjB,GAAG,EAAE,CAAC;IACN,GAAG,EAAE,CAAC;CACP,CAAA;AAED,MAAM,UAAU,MAAM,CACpB,iBAAoC,EACpC,OAAuE,EACvE,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,KAAK,KAAoB,EAAE;IAEhE;;;;;OAKG;IACH,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE,CAAC;QAChD,OAAO,GAAG,EAAE,GAAE,CAAC,CAAA;IACjB,CAAC;IAED,MAAM,QAAQ,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAA;IAEnD,MAAM,mBAAmB,GAAG,IAAI,OAAO,EAA8B,CAAA;IAErE,MAAM,oBAAoB,GAAiC,CAAC,OAAO,EAAE,EAAE;QACrE,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YACxB,MAAM,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAEnD;;;eAGG;YACH,IAAI,KAAK,CAAC,cAAc,KAAK,OAAO,CAAC,KAAK,CAAC;gBAAE,OAAM;YAEnD,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,CAAA;gBAC/B,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzB,mBAAmB,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;gBACjD,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBAClC,CAAC;YACH,CAAC;iBAAM,IAAI,KAAK,EAAE,CAAC;gBACjB,KAAK,CAAC,KAAK,CAAC,CAAA;gBACZ,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IAED,MAAM,QAAQ,GAAG,IAAI,oBAAoB,CAAC,oBAAoB,EAAE;QAC9D,IAAI;QACJ,UAAU;QACV,SAAS,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC;KACpE,CAAC,CAAA;IAEF,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAA;IAExD,OAAO,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;AACpC,CAAC"}