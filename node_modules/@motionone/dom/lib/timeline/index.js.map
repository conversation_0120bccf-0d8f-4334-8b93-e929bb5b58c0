{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/timeline/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAA;AAOtC,OAAO,EACL,QAAQ,EACR,aAAa,EACb,UAAU,EACV,QAAQ,EACR,iBAAiB,EACjB,QAAQ,GACT,MAAM,kBAAkB,CAAA;AACzB,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAA;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAA;AAKvD,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAA;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,4BAA4B,CAAA;AAC1D,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAA;AACrD,OAAO,EAAE,eAAe,EAAE,MAAM,2BAA2B,CAAA;AAM3D,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAA;AAChD,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAA;AAC3C,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAA;AAC5C,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAchD,MAAM,UAAU,QAAQ,CACtB,UAA8B,EAC9B,UAA2B,EAAE;;IAE7B,MAAM,oBAAoB,GAAG,4BAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IAE9E;;OAEG;IACH,MAAM,kBAAkB,GAAG,oBAAoB;SAC5C,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,YAAY,CAAC,GAAG,UAAU,EAAE,SAAS,CAAC,CAAC;SAC3D,MAAM,CAAC,OAAO,CAAC,CAAA;IAElB,OAAO,YAAY,CACjB,kBAAkB,EAClB,OAAO;IACP,uDAAuD;IACvD,MAAA,oBAAoB,CAAC,CAAC,CAAC,0CAAG,CAAC,EAAE,QAAQ,CACtC,CAAA;AACH,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,UAA8B,EAC9B,KAA+D,EAAE;QAAjE,EAAE,cAAc,GAAG,EAAE,OAA4C,EAAvC,eAAe,cAAzC,kBAA2C,CAAF;IAEzC,MAAM,oBAAoB,GAA6B,EAAE,CAAA;IACzD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA4B,CAAA;IAC5D,MAAM,YAAY,GAAG,EAAE,CAAA;IACvB,MAAM,UAAU,GAAG,IAAI,GAAG,EAAkB,CAAA;IAE5C,IAAI,QAAQ,GAAG,CAAC,CAAA;IAChB,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,IAAI,aAAa,GAAG,CAAC,CAAA;IAErB;;;;OAIG;IACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAA;QAE7B;;WAEG;QACH,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,UAAU,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;YACpC,SAAQ;QACV,CAAC;aAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnC,UAAU,CAAC,GAAG,CACZ,OAAO,CAAC,IAAI,EACZ,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,CAC5D,CAAA;YACD,SAAQ;QACV,CAAC;QAED,MAAM,CAAC,iBAAiB,EAAE,SAAS,EAAE,OAAO,GAAG,EAAE,CAAC,GAAG,OAAO,CAAA;QAE5D;;;WAGG;QACH,IAAI,OAAO,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;YAC7B,WAAW,GAAG,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAA;QAC3E,CAAC;QAED;;;WAGG;QACH,IAAI,WAAW,GAAG,CAAC,CAAA;QAEnB;;;WAGG;QACH,MAAM,QAAQ,GAAG,eAAe,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAA;QACjE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAA;QACnC,KAAK,IAAI,YAAY,GAAG,CAAC,EAAE,YAAY,GAAG,WAAW,EAAE,YAAY,EAAE,EAAE,CAAC;YACtE,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAA;YACtC,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;YAErE,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC5B,MAAM,aAAa,GAAG,gBAAgB,CAAC,GAAG,EAAE,eAAe,CAAC,CAAA;gBAC5D,IAAI,cAAc,GAAG,aAAa,CAChC,SAAS,CAAC,GAA6B,CAAE,CAC1C,CAAA;gBACD,MAAM,YAAY,GAAG,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAA;gBAC7C,IAAI,EACF,QAAQ,GAAG,cAAc,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EACvD,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,GAClD,GAAG,YAAY,CAAA;gBAEhB,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC9B,SAAS,CACP,GAAG,KAAK,SAAS,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAC9C,uDAAuD,CACxD,CAAA;oBAED,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CACnC,cAAc,EACd,GAAG,KAAK,SAAS,EACjB,GAAG,EAAE,CAAC,CAAC,EACP,GAAG,CACJ,CAAA;oBAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;oBACtB,cAAc,GAAG,MAAM,CAAC,SAAS,IAAI,cAAc,CAAA;oBACnD,QAAQ,GAAG,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAA;gBACxC,CAAC;gBAED,MAAM,KAAK,GACT,aAAa,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,CAAA;gBAC9D,MAAM,SAAS,GAAG,WAAW,GAAG,KAAK,CAAA;gBACrC,MAAM,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAA;gBAEvC;;mBAEG;gBACH,IAAI,EAAE,MAAM,GAAG,aAAa,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,GAAG,YAAY,CAAA;gBAEpE;;;;mBAIG;gBACH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC3C,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;gBACf,CAAC;gBAED;;mBAEG;gBACH,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAA;gBACvD,SAAS,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;gBAE9C;;;;mBAIG;gBACH,cAAc,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBAE3D;;mBAEG;gBACH,YAAY,CACV,aAAa,EACb,cAAc,EACd,MAA2B,EAC3B,MAAM,EACN,SAAS,EACT,UAAU,CACX,CAAA;gBAED,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,QAAQ,EAAE,WAAW,CAAC,CAAA;gBACrD,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAA;YACrD,CAAC;QACH,CAAC;QAED,QAAQ,GAAG,WAAW,CAAA;QACtB,WAAW,IAAI,WAAW,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE;QACnD,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;YACjC,MAAM,aAAa,GAAG,cAAc,CAAC,GAAG,CAAC,CAAA;YAEzC;;eAEG;YACH,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;YAEjC,MAAM,SAAS,GAA8B,EAAE,CAAA;YAC/C,MAAM,WAAW,GAAa,EAAE,CAAA;YAChC,MAAM,WAAW,GAAa,EAAE,CAAA;YAEhC;;;eAGG;YACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAA;gBAC9C,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;gBACrB,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,CAAA;gBAChD,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAA;YAC7C,CAAC;YAED;;;;eAIG;YACH,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;gBACtB,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;gBAC/B,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;YAC/B,CAAC;YAED;;;;eAIG;YACH,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9C,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACnB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtB,CAAC;YAED,oBAAoB,CAAC,IAAI,CAAC;gBACxB,OAAO;gBACP,GAAG;gBACH,SAAS;8DAEJ,cAAc,KACjB,QAAQ,EAAE,aAAa,EACvB,MAAM,EAAE,WAAW,EACnB,MAAM,EAAE,WAAW,KAChB,eAAe;aAErB,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,oBAAoB,CAAA;AAC7B,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAgB,EAChB,SAAwC;IAExC,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IACrD,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAA;AAChC,CAAC;AAED,SAAS,gBAAgB,CACvB,IAAY,EACZ,SAA0B;IAE1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAAE,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;IAC1C,OAAO,SAAS,CAAC,IAAI,CAAC,CAAA;AACxB,CAAC"}