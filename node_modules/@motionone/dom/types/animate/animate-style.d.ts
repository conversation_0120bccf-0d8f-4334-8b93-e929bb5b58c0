import type { AnimationFactory, ValueKeyframesDefinition } from "./types";
import type { Animation } from "@motionone/animation";
import { AnimationOptions } from "@motionone/types";
export declare function animateStyle(element: Element, key: string, keyframesDefinition: ValueKeyframesDefinition, options?: AnimationOptions, AnimationPolyfill?: typeof Animation): AnimationFactory;
//# sourceMappingURL=animate-style.d.ts.map