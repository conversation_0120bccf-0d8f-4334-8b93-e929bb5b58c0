const t=t=>"number"==typeof t,e=t=>t,n=t=>"string"==typeof t,a=t=>t/1e3;const r=5;function o(t,e,n){const a=Math.max(e-r,0);return o=n-t(a),(s=e-a)?o*(1e3/s):0;var o,s}const s=100,i=10,c=1;const u=10,l=1e4;const h=["","X","Y","Z"],f={x:"translateX",y:"translateY",z:"translateZ"},g={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},d={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:g,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:e},skew:g},p=new Map,m=t=>`--motion-${t}`,v=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{h.forEach((e=>{v.push(t+e),p.set(m(t+e),d[t])}))}));const M=new Set(v),x=t=>M.has(t);function y(e){return t(e)&&!isNaN(e)}function b(t){return n(t)?parseFloat(t):t}const w=function(t){const a=new WeakMap;return(r={})=>{const s=new Map,i=(e=0,n=100,a=0,o=!1)=>{const i=`${e}-${n}-${a}-${o}`;return s.has(i)||s.set(i,t(Object.assign({from:e,to:n,velocity:a},r))),s.get(i)},c=(t,n)=>(a.has(t)||a.set(t,function(t,n=e){let a,r=u,o=t(0);const s=[n(o.current)];for(;!o.done&&r<l;)o=t(r),s.push(n(o.done?o.target:o.current)),void 0===a&&o.hasReachedTarget&&(a=r),r+=u;const i=r-u;return 1===s.length&&s.push(o.current),{keyframes:s,duration:i/1e3,overshootDuration:(null!=a?a:i)/1e3}}(t,n)),a.get(t));return{createAnimation:(t,a=!0,r,s,u)=>{let l,h,g,d=0,v=e;const M=t.length;if(a){v=function(t,a){var r;let o=(null==a?void 0:a.toDefaultUnit)||e;const s=t[t.length-1];if(n(s)){const t=(null===(r=s.match(/(-?[\d.]+)([a-z%]*)/))||void 0===r?void 0:r[2])||"";t&&(o=e=>e+t)}return o}(t,s?p.get((f[w=s]&&(w=f[w]),x(w)?m(w):w)):void 0);if(g=b(t[M-1]),M>1&&null!==t[0])h=b(t[0]);else{const t=null==u?void 0:u.generator;if(t){const{animation:e,generatorStartTime:n}=u,a=(null==e?void 0:e.startTime)||n||0,r=(null==e?void 0:e.currentTime)||performance.now()-a,s=t(r).current;h=s,d=o((e=>t(e).current),r,s)}else r&&(h=b(r()))}}var w;if(y(h)&&y(g)){const t=i(h,g,d,null==s?void 0:s.includes("scale"));l=Object.assign(Object.assign({},c(t,v)),{easing:"linear"}),u&&(u.generator=t,u.generatorStartTime=performance.now())}if(!l){l={easing:"ease",duration:c(i(0,100)).overshootDuration}}return l}}}}((({stiffness:t=s,damping:e=i,mass:n=c,from:r=0,to:u=1,velocity:l=0,restSpeed:h,restDistance:f}={})=>{l=l?a(l):0;const g={done:!1,hasReachedTarget:!1,current:r,target:u},d=u-r,p=Math.sqrt(t/n)/1e3,m=((t=s,e=i,n=c)=>e/(2*Math.sqrt(t*n)))(t,e,n),v=Math.abs(d)<5;let M;if(h||(h=v?.01:2),f||(f=v?.005:.5),m<1){const t=p*Math.sqrt(1-m*m);M=e=>u-Math.exp(-m*p*e)*((m*p*d-l)/t*Math.sin(t*e)+d*Math.cos(t*e))}else M=t=>u-Math.exp(-p*t)*(d+(p*d-l)*t);return t=>{g.current=M(t);const e=0===t?l:o(M,t,g.current),n=Math.abs(e)<=h,a=Math.abs(u-g.current)<=f;var s,i,c;return g.done=n&&a,g.hasReachedTarget=(s=r,i=u,c=g.current,s<i&&c>=i||s>i&&c<=i),g}}));export{w as spring};
