const e={any:0,all:1};function t(t,n,{root:r,margin:o,amount:c="any"}={}){if("undefined"==typeof IntersectionObserver)return()=>{};const s=function(e,t){var n;return"string"==typeof e?t?(null!==(n=t[e])&&void 0!==n||(t[e]=document.querySelectorAll(e)),e=t[e]):e=document.querySelectorAll(e):e instanceof Element&&(e=[e]),Array.from(e||[])}(t),i=new WeakMap,a=new IntersectionObserver((e=>{e.forEach((e=>{const t=i.get(e.target);if(e.isIntersecting!==Boolean(t))if(e.isIntersecting){const t=n(e);"function"==typeof t?i.set(e.target,t):a.unobserve(e.target)}else t&&(t(e),i.delete(e.target))}))}),{root:r,rootMargin:o,threshold:"number"==typeof c?c:e[c]});return s.forEach((e=>a.observe(e))),()=>a.disconnect()}export{t as inView};
