var t={d:(e,n)=>{for(var i in n)t.o(n,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:n[i]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},e={};t.d(e,{j:()=>nt});const n={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},i=()=>{},a=t=>t,s=t=>"object"==typeof t&&Boolean(t.createAnimation),r=t=>"number"==typeof t,o=t=>Array.isArray(t)&&!r(t[0]),l=(t,e,n)=>-n*t+n*e+t,c=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function u(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const a=c(0,e,i);t.push(l(n,1,a))}}const h=(t,e,n)=>Math.min(Math.max(n,t),e);const d=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,f=1e-7,p=12;function m(t,e,n,i){if(t===e&&n===i)return a;return a=>0===a||1===a?a:d(function(t,e,n,i,a){let s,r,o=0;do{r=e+(n-e)/2,s=d(r,i,a)-t,s>0?n=r:e=r}while(Math.abs(s)>f&&++o<p);return r}(a,0,1,t,n),e,i)}const y=t=>"function"==typeof t,g=t=>Array.isArray(t)&&r(t[0]),v={ease:m(.25,.1,.25,1),"ease-in":m(.42,0,1,1),"ease-in-out":m(.42,0,.58,1),"ease-out":m(0,0,.58,1)},T=/\((.*?)\)/;function w(t){if(y(t))return t;if(g(t))return m(...t);const e=v[t];if(e)return e;if(t.startsWith("steps")){const e=T.exec(t);if(e){const t=e[1].split(",");return((t,e="end")=>n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,a="end"===e?Math.floor(i):Math.ceil(i);return h(0,1,a/t)})(parseFloat(t[0]),t[1].trim())}}return a}class b{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const S=new WeakMap;function D(t){return S.has(t)||S.set(t,{transforms:[],values:new Map}),S.get(t)}const O=["","X","Y","Z"],A={x:"translateX",y:"translateY",z:"translateZ"},x={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},k={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:x,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:a},skew:x},M=new Map,j=t=>`--motion-${t}`,E=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{O.forEach((e=>{E.push(t+e),M.set(j(t+e),k[t])}))}));const P=(t,e)=>E.indexOf(t)-E.indexOf(e),R=new Set(E),V=t=>R.has(t),$=t=>t.sort(P).reduce(q,"").trim(),q=(t,e)=>`${t} ${e}(var(${j(e)}))`,F=t=>t.startsWith("--"),U=new Set,B=t=>1e3*t,C=t=>t/1e3,I=(t,e)=>document.createElement("div").animate(t,e),_={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{I({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(I({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{I({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},z={},W={};for(const t in _)W[t]=()=>(void 0===z[t]&&(z[t]=_[t]()),z[t]);const K=(t,e)=>y(t)?W.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(c(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,e)})`:n.easing:g(t)?N(t):t,N=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`;function X(t){return A[t]&&(t=A[t]),V(t)?j(t):t}const Y=(t,e)=>{e=X(e);let n=F(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=M.get(e);t&&(n=t.initialValue)}return n},Z=(t,e,n)=>{e=X(e),F(e)?t.style.setProperty(e,n):t.style[e]=n};function L(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function G(t,e,l,c={},u){const h=window.__MOTION_DEV_TOOLS_RECORD,d=!1!==c.record&&h;let f,{duration:p=n.duration,delay:m=n.delay,endDelay:g=n.endDelay,repeat:v=n.repeat,easing:T=n.easing,persist:w=!1,direction:S,offset:O,allowWebkitAcceleration:x=!1,autoplay:k=!0}=c;const j=D(t),E=V(e);let P=W.waapi();E&&((t,e)=>{A[e]&&(e=A[e]);const{transforms:n}=D(t);var i,a;a=e,-1===(i=n).indexOf(a)&&i.push(a),t.style.transform=$(n)})(t,e);const R=X(e),q=function(t,e){return t.has(e)||t.set(e,new b),t.get(e)}(j.values,R),C=M.get(R);return L(q.animation,!(s(T)&&q.generator)&&!1!==c.record),()=>{const n=()=>{var e,n;return null!==(n=null!==(e=Y(t,R))&&void 0!==e?e:null==C?void 0:C.initialValue)&&void 0!==n?n:0};let b=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}((t=>Array.isArray(t)?t:[t])(l),n);const D=function(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||a;const s=t[t.length-1];if("string"==typeof s){const t=(null===(n=s.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}(b,C);if(s(T)){const t=T.createAnimation(b,"opacity"!==e,n,R,q);T=t.easing,b=t.keyframes||b,p=t.duration||p}if(F(R)&&(W.cssRegisterProperty()?function(t){if(!U.has(t)){U.add(t);try{const{syntax:e,initialValue:n}=M.has(t)?M.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(R):P=!1),E&&!W.linearEasing()&&(y(T)||o(T)&&T.some(y))&&(P=!1),P){C&&(b=b.map((t=>r(t)?C.toDefaultUnit(t):t))),1!==b.length||W.partialKeyframes()&&!d||b.unshift(n());const e={delay:B(m),duration:B(p),endDelay:B(g),easing:o(T)?void 0:K(T,p),direction:S,iterations:v+1,fill:"both"};f=t.animate({[R]:b,offset:O,easing:o(T)?T.map((t=>K(t,p))):void 0},e),f.finished||(f.finished=new Promise(((t,e)=>{f.onfinish=t,f.oncancel=e})));const a=b[b.length-1];f.finished.then((()=>{w||(Z(t,R,a),f.cancel())})).catch(i),x||(f.playbackRate=1.000001)}else if(u&&E)b=b.map((t=>"string"==typeof t?parseFloat(t):t)),1===b.length&&b.unshift(parseFloat(n())),f=new u((e=>{Z(t,R,D?D(e):e)}),b,Object.assign(Object.assign({},c),{duration:p,easing:T}));else{const e=b[b.length-1];Z(t,R,C&&r(e)?C.toDefaultUnit(e):e)}return d&&h(t,e,b,{duration:p,delay:m,easing:T,repeat:v,offset:O},"motion-one"),q.setAnimation(f),f&&!k&&f.pause(),f}}const H=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t),J=t=>t(),Q={get:(t,e)=>{const n=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return C((null==n?void 0:n[e])||0);case"playbackRate":case"playState":return null==n?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(tt)).catch(i)),t.finished;case"stop":return()=>{t.animations.forEach((t=>L(t)))};case"forEachNative":return e=>{t.animations.forEach((n=>e(n,t)))};default:return void 0===(null==n?void 0:n[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,n)=>{switch(e){case"currentTime":n=B(n);case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=n;return!0}return!1}},tt=t=>t.finished;function et(t,e,n){return y(t)?t(e,n):t}const nt=(it=class{constructor(t,e=[0,1],{easing:i,duration:r=n.duration,delay:d=n.delay,endDelay:f=n.endDelay,repeat:p=n.repeat,offset:m,direction:y="normal",autoplay:g=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=a,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),i=i||n.easing,s(i)){const t=i.createAnimation(e);i=t.easing,e=t.keyframes||e,r=t.duration||r}this.repeat=p,this.easing=o(i)?a:w(i),this.updateDuration(r);const v=function(t,e=function(t){const e=[0];return u(e,t-1),e}(t.length),n=a){const i=t.length,s=i-e.length;return s>0&&u(e,s),a=>{let s=0;for(;s<i-2&&!(a<e[s+1]);s++);let r=h(0,1,c(e[s],e[s+1],a));const u=function(t,e){return o(t)?t[((t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t})(0,t.length,e)]:t}(n,s);return r=u(r),l(t[s],t[s+1],r)}}(e,m,o(i)?i.map(w):a);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-d,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const a=i/this.duration;let s=Math.floor(a),r=a%1;!r&&a>=1&&(r=1),1===r&&s--;const o=s%2;("reverse"===y||"alternate"===y&&o||"alternate-reverse"===y&&!o)&&(r=1-r);const l=i>=this.totalDuration?1:Math.min(r,1),c=v(this.easing(l));t(c),void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+f)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,c)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},g&&this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}},function(t,e,i={}){const a=(t=function(t,e){return"string"==typeof t?t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}(t)).length;Boolean(a),Boolean(e);const s=[];for(let n=0;n<a;n++){const r=t[n];for(const t in e){const o=H(i,t);o.delay=et(o.delay,n,a);const l=G(r,t,e[t],o,it);s.push(l)}}return((t,e,i=n.duration)=>new Proxy({animations:t.map(J).filter(Boolean),duration:i,options:e},Q))(s,i,i.duration)});var it,at=e.j;export{at as animate};