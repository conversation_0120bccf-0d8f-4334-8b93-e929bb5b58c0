const e=new WeakMap;let t;function n({target:t,contentRect:n,borderBoxSize:o}){var i;null===(i=e.get(t))||void 0===i||i.forEach((e=>{e({target:t,contentSize:n,get size(){return function(e,t){if(t){const{inlineSize:e,blockSize:n}=t[0];return{width:e,height:n}}return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}(t,o)}})}))}function o(e){e.forEach(n)}function i(n,i){t||"undefined"!=typeof ResizeObserver&&(t=new ResizeObserver(o));const r=function(e,t){var n;return"string"==typeof e?t?(null!==(n=t[e])&&void 0!==n||(t[e]=document.querySelectorAll(e)),e=t[e]):e=document.querySelectorAll(e):e instanceof Element&&(e=[e]),Array.from(e||[])}(n);return r.forEach((n=>{let o=e.get(n);o||(o=new Set,e.set(n,o)),o.add(i),null==t||t.observe(n)})),()=>{r.forEach((n=>{const o=e.get(n);null==o||o.delete(i),(null==o?void 0:o.size)||null==t||t.unobserve(n)}))}}const r=new Set;let c;function l(e){return r.add(e),c||(c=()=>{const e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};r.forEach((e=>e(t)))},window.addEventListener("resize",c)),()=>{r.delete(e),!r.size&&c&&(c=void 0)}}function u(e,t){return"function"==typeof e?l(e):i(e,t)}export{u as resize};
