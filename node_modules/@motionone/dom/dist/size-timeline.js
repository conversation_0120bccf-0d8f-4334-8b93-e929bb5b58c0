"function"==typeof SuppressedError&&SuppressedError;var t=function(){};function e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}"production"!==process.env.NODE_ENV&&(t=function(t,e){if(!t)throw new Error(e)});const n=(t,e,n)=>Math.min(Math.max(n,t),e),i={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},a=t=>"number"==typeof t,s=t=>Array.isArray(t)&&!a(t[0]),r=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};function o(t,e){return s(t)?t[r(0,t.length,e)]:t}const l=(t,e,n)=>-n*t+n*e+t,c=()=>{},u=t=>t,h=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function f(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const a=h(0,e,i);t.push(l(n,1,a))}}function p(t){const e=[0];return f(e,t-1),e}const d=t=>Array.isArray(t)&&a(t[0]),m=t=>"object"==typeof t&&Boolean(t.createAnimation),y=t=>"function"==typeof t,g=t=>"string"==typeof t,v={ms:t=>1e3*t,s:t=>t/1e3},O=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,b=1e-7,w=12;function S(t,e,n,i){if(t===e&&n===i)return u;const a=e=>function(t,e,n,i,a){let s,r,o=0;do{r=e+(n-e)/2,s=O(r,i,a)-t,s>0?n=r:e=r}while(Math.abs(s)>b&&++o<w);return r}(e,0,1,t,n);return t=>0===t||1===t?t:O(a(t),e,i)}const T={ease:S(.25,.1,.25,1),"ease-in":S(.42,0,1,1),"ease-in-out":S(.42,0,.58,1),"ease-out":S(0,0,.58,1)},x=/\((.*?)\)/;function A(t){if(y(t))return t;if(d(t))return S(...t);const e=T[t];if(e)return e;if(t.startsWith("steps")){const e=x.exec(t);if(e){const t=e[1].split(",");return((t,e="end")=>i=>{const a=(i="end"===e?Math.min(i,.999):Math.max(i,.001))*t,s="end"===e?Math.floor(a):Math.ceil(a);return n(0,1,s/t)})(parseFloat(t[0]),t[1].trim())}}return u}class D{constructor(t,e=[0,1],{easing:a,duration:r=i.duration,delay:c=i.delay,endDelay:d=i.endDelay,repeat:y=i.repeat,offset:g,direction:v="normal",autoplay:O=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=u,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),a=a||i.easing,m(a)){const t=a.createAnimation(e);a=t.easing,e=t.keyframes||e,r=t.duration||r}this.repeat=y,this.easing=s(a)?u:A(a),this.updateDuration(r);const b=function(t,e=p(t.length),i=u){const a=t.length,s=a-e.length;return s>0&&f(e,s),s=>{let r=0;for(;r<a-2&&!(s<e[r+1]);r++);let c=n(0,1,h(e[r],e[r+1],s));return c=o(i,r)(c),l(t[r],t[r+1],c)}}(e,g,s(a)?a.map(A):u);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-c,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const a=i/this.duration;let s=Math.floor(a),r=a%1;!r&&a>=1&&(r=1),1===r&&s--;const o=s%2;("reverse"===v||"alternate"===v&&o||"alternate-reverse"===v&&!o)&&(r=1-r);const l=i>=this.totalDuration?1:Math.min(r,1),u=b(this.easing(l));t(u);void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+d)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,u)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},O&&this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}function E(t,e,n){return y(t)?t(e,n):t}class M{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const k=new WeakMap;function j(t){return k.has(t)||k.set(t,{transforms:[],values:new Map}),k.get(t)}const P=["","X","Y","Z"],R={x:"translateX",y:"translateY",z:"translateZ"},V={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},$={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:V,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:u},skew:V},q=new Map,F=t=>`--motion-${t}`,I=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{P.forEach((e=>{I.push(t+e),q.set(F(t+e),$[t])}))}));const U=(t,e)=>I.indexOf(t)-I.indexOf(e),W=new Set(I),_=t=>W.has(t),C=(t,e)=>{R[e]&&(e=R[e]);const{transforms:n}=j(t);var i,a;a=e,-1===(i=n).indexOf(a)&&i.push(a),t.style.transform=z(n)},z=t=>t.sort(U).reduce(B,"").trim(),B=(t,e)=>`${t} ${e}(var(${F(e)}))`,N=t=>t.startsWith("--"),K=new Set;const X=(t,e)=>document.createElement("div").animate(t,e),Y={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{X({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(X({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{X({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},Z={},L={};for(const t in Y)L[t]=()=>(void 0===Z[t]&&(Z[t]=Y[t]()),Z[t]);const G=(t,e)=>y(t)?L.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(h(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,e)})`:i.easing:d(t)?H(t):t,H=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`;const J=t=>Array.isArray(t)?t:[t];function Q(t){return R[t]&&(t=R[t]),_(t)?F(t):t}const tt={get:(t,e)=>{e=Q(e);let n=N(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=q.get(e);t&&(n=t.initialValue)}return n},set:(t,e,n)=>{e=Q(e),N(e)?t.style.setProperty(e,n):t.style[e]=n}};function et(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function nt(t,e,n,r={},o){const l=window.__MOTION_DEV_TOOLS_RECORD,h=!1!==r.record&&l;let f,{duration:p=i.duration,delay:d=i.delay,endDelay:O=i.endDelay,repeat:b=i.repeat,easing:w=i.easing,persist:S=!1,direction:T,offset:x,allowWebkitAcceleration:A=!1,autoplay:D=!0}=r;const E=j(t),k=_(e);let P=L.waapi();k&&C(t,e);const R=Q(e),V=function(t,e){return t.has(e)||t.set(e,new M),t.get(e)}(E.values,R),$=q.get(R);return et(V.animation,!(m(w)&&V.generator)&&!1!==r.record),()=>{const i=()=>{var e,n;return null!==(n=null!==(e=tt.get(t,R))&&void 0!==e?e:null==$?void 0:$.initialValue)&&void 0!==n?n:0};let E=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}(J(n),i);const M=function(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||u;const a=t[t.length-1];if(g(a)){const t=(null===(n=a.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}(E,$);if(m(w)){const t=w.createAnimation(E,"opacity"!==e,i,R,V);w=t.easing,E=t.keyframes||E,p=t.duration||p}if(N(R)&&(L.cssRegisterProperty()?function(t){if(!K.has(t)){K.add(t);try{const{syntax:e,initialValue:n}=q.has(t)?q.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(R):P=!1),k&&!L.linearEasing()&&(y(w)||s(w)&&w.some(y))&&(P=!1),P){$&&(E=E.map((t=>a(t)?$.toDefaultUnit(t):t))),1!==E.length||L.partialKeyframes()&&!h||E.unshift(i());const e={delay:v.ms(d),duration:v.ms(p),endDelay:v.ms(O),easing:s(w)?void 0:G(w,p),direction:T,iterations:b+1,fill:"both"};f=t.animate({[R]:E,offset:x,easing:s(w)?w.map((t=>G(t,p))):void 0},e),f.finished||(f.finished=new Promise(((t,e)=>{f.onfinish=t,f.oncancel=e})));const n=E[E.length-1];f.finished.then((()=>{S||(tt.set(t,R,n),f.cancel())})).catch(c),A||(f.playbackRate=1.000001)}else if(o&&k)E=E.map((t=>"string"==typeof t?parseFloat(t):t)),1===E.length&&E.unshift(parseFloat(i())),f=new o((e=>{tt.set(t,R,M?M(e):e)}),E,Object.assign(Object.assign({},r),{duration:p,easing:w}));else{const e=E[E.length-1];tt.set(t,R,$&&a(e)?$.toDefaultUnit(e):e)}return h&&l(t,e,E,{duration:p,delay:d,easing:w,repeat:b,offset:x},"motion-one"),V.setAnimation(f),f&&!D&&f.pause(),f}}const it=t=>t(),at={get:(t,e)=>{const n=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return v.s((null==n?void 0:n[e])||0);case"playbackRate":case"playState":return null==n?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(st)).catch(c)),t.finished;case"stop":return()=>{t.animations.forEach((t=>et(t)))};case"forEachNative":return e=>{t.animations.forEach((n=>e(n,t)))};default:return void 0===(null==n?void 0:n[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,n)=>{switch(e){case"currentTime":n=v.ms(n);case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=n;return!0}return!1}},st=t=>t.finished,rt=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);function ot(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}function lt(t,e,n,i){var s;return a(e)?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(s=i.get(e))&&void 0!==s?s:t}function ct(t,n,i,a,s,r){!function(t,n,i){for(let a=0;a<t.length;a++){const s=t[a];s.at>n&&s.at<i&&(e(t,s),a--)}}(t,s,r);for(let e=0;e<n.length;e++)t.push({value:n[e],at:l(s,r,a[e]),easing:o(i,e)})}function ut(t,e){return t.at===e.at?null===t.value?1:-1:t.at-e.at}function ht(t,e={}){var n;const a=ft(t,e),s=a.map((t=>nt(...t,D))).filter(Boolean);return((t,e,n=i.duration)=>new Proxy({animations:t.map(it).filter(Boolean),duration:n,options:e},at))(s,e,null===(n=a[0])||void 0===n?void 0:n[3].duration)}function ft(e,n={}){var{defaultOptions:a={}}=n,s=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]])}return n}(n,["defaultOptions"]);const r=[],o=new Map,l={},c=new Map;let u=0,d=0,y=0;for(let n=0;n<e.length;n++){const s=e[n];if(g(s)){c.set(s,d);continue}if(!Array.isArray(s)){c.set(s.name,lt(d,s.at,u,c));continue}const[r,h,v={}]=s;void 0!==v.at&&(d=lt(d,v.at,u,c));let O=0;const b=ot(r,l),w=b.length;for(let e=0;e<w;e++){const n=pt(b[e],o);for(const s in h){const r=dt(s,n);let o=J(h[s]);const l=rt(v,s);let{duration:c=a.duration||i.duration,easing:u=a.easing||i.easing}=l;if(m(u)){t("opacity"===s||o.length>1,"spring must be provided 2 keyframes within timeline()");const e=u.createAnimation(o,"opacity"!==s,(()=>0),s);u=e.easing,o=e.keyframes||o,c=e.duration||c}const g=E(v.delay,e,w)||0,b=d+g,S=b+c;let{offset:T=p(o.length)}=l;1===T.length&&0===T[0]&&(T[1]=1);const x=T.length-o.length;x>0&&f(T,x),1===o.length&&o.unshift(null),ct(r,o,u,T,b,S),O=Math.max(g+c,O),y=Math.max(S,y)}}u=d,d+=O}return o.forEach(((t,e)=>{for(const n in t){const o=t[n];o.sort(ut);const l=[],c=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:a}=o[t];l.push(n),c.push(h(0,y,e)),u.push(a||i.easing)}0!==c[0]&&(c.unshift(0),l.unshift(l[0]),u.unshift("linear")),1!==c[c.length-1]&&(c.push(1),l.push(null)),r.push([e,n,l,Object.assign(Object.assign(Object.assign({},a),{duration:y,easing:u,offset:c}),s)])}})),r}function pt(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function dt(t,e){return e[t]||(e[t]=[]),e[t]}export{ft as createAnimationsFromTimeline,ht as timeline};
