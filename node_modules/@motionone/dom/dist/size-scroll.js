"function"==typeof SuppressedError&&SuppressedError;const t=new WeakMap;let e;function n({target:e,contentRect:n,borderBoxSize:o}){var r;null===(r=t.get(e))||void 0===r||r.forEach((t=>{t({target:e,contentSize:n,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(e,o)}})}))}function o(t){t.forEach(n)}function r(n,r){e||"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(o));const i=function(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}(n);return i.forEach((n=>{let o=t.get(n);o||(o=new Set,t.set(n,o)),o.add(r),null==e||e.observe(n)})),()=>{i.forEach((n=>{const o=t.get(n);null==o||o.delete(r),(null==o?void 0:o.size)||null==e||e.unobserve(n)}))}}const i=new Set;let s;function c(t){return i.add(t),s||(s=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};i.forEach((t=>t(e)))},window.addEventListener("resize",s)),()=>{i.delete(t),!i.size&&s&&(s=void 0)}}const f=(t,e,n)=>Math.min(Math.max(n,t),e),l=t=>"number"==typeof t,a=t=>Array.isArray(t)&&!l(t[0]),u=(t,e,n)=>{const o=e-t;return((n-t)%o+o)%o+t};const g=(t,e,n)=>-n*t+n*e+t,h=t=>t,d=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function p(t,e){const n=t[t.length-1];for(let o=1;o<=e;o++){const r=d(0,e,o);t.push(g(n,1,r))}}function m(t){const e=[0];return p(e,t-1),e}function y(t,e=m(t.length),n=h){const o=t.length,r=o-e.length;return r>0&&p(e,r),r=>{let i=0;for(;i<o-2&&!(r<e[i+1]);i++);let s=f(0,1,d(e[i],e[i+1],r));const c=function(t,e){return a(t)?t[u(0,t.length,e)]:t}(n,i);return s=c(s),g(t[i],t[i+1],s)}}const v=t=>"function"==typeof t,w=t=>"string"==typeof t;const E=50,x={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function L(t,e,n,o){const r=n[e],{length:i,position:s}=x[e],c=r.current,f=n.time;r.current=t[`scroll${s}`],r.scrollLength=t[`scroll${i}`]-t[`client${i}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=d(0,r.scrollLength,r.current);const l=o-f;var a,u;r.velocity=l>E?0:(a=r.current-c,(u=l)?a*(1e3/u):0)}const O={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},W={start:0,center:.5,end:1};function S(t,e,n=0){let o=0;if(void 0!==W[t]&&(t=W[t]),w(t)){const e=parseFloat(t);t.endsWith("px")?o=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?o=e/100*document.documentElement.clientWidth:t.endsWith("vh")?o=e/100*document.documentElement.clientHeight:t=e}return l(t)&&(o=e*t),n+o}const z=[0,0];function b(t,e,n,o){let r=Array.isArray(t)?t:z,i=0,s=0;return l(t)?r=[t,t]:w(t)&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,W[t]?t:"0"]),i=S(r[0],n,o),s=S(r[1],e),i-s}const A={x:0,y:0};function H(t,e,n){let{offset:o=O.All}=n;const{target:r=t,axis:i="y"}=n,s="y"===i?"height":"width",c=r!==t?function(t,e){let n={x:0,y:0},o=t;for(;o&&o!==e;)if(o instanceof HTMLElement)n.x+=o.offsetLeft,n.y+=o.offsetTop,o=o.offsetParent;else if(o instanceof SVGGraphicsElement&&"getBBox"in o){const{top:t,left:e}=o.getBBox();for(n.x+=e,n.y+=t;o&&"svg"!==o.tagName;)o=o.parentNode}return n}(r,t):A,f=r===t?{width:t.scrollWidth,height:t.scrollHeight}:{width:r.clientWidth,height:r.clientHeight},l={width:t.clientWidth,height:t.clientHeight};e[i].offset.length=0;let a=!e[i].interpolate;const u=o.length;for(let t=0;t<u;t++){const n=b(o[t],l[s],f[s],c[i]);a||n===e[i].interpolatorOffsets[t]||(a=!0),e[i].offset[t]=n}a&&(e[i].interpolate=y(m(u),e[i].offset),e[i].interpolatorOffsets=[...e[i].offset]),e[i].progress=e[i].interpolate(e[i].current)}function B(t,e,n,o={}){const r=o.axis||"y";return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let o=e;for(;o&&o!=t;)n.x.targetOffset+=o.offsetLeft,n.y.targetOffset+=o.offsetTop,o=o.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,o.target,n),update:e=>{!function(t,e,n){L(t,"x",e,n),L(t,"y",e,n),e.time=n}(t,n,e),(o.offset||o.target)&&H(t,n,o)},notify:v(e)?()=>e(n):M(e,n[r])}}function M(t,e){return t.pause(),t.forEachNative(((t,{easing:e})=>{var n,o;if(t.updateDuration)e||(t.easing=h),t.updateDuration(1);else{const r={duration:1e3};e||(r.easing="linear"),null===(o=null===(n=t.effect)||void 0===n?void 0:n.updateTiming)||void 0===o||o.call(n,r)}})),()=>{t.currentTime=e.progress}}const T=new WeakMap,k=new WeakMap,P=new WeakMap,j=t=>t===document.documentElement?window:t;function q(t,e={}){var{container:n=document.documentElement}=e,o=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(t);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]])}return n}(e,["container"]);let i=P.get(n);i||(i=new Set,P.set(n,i));const s=B(n,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},o);if(i.add(s),!T.has(n)){const t=()=>{const t=performance.now();for(const t of i)t.measure();for(const e of i)e.update(t);for(const t of i)t.notify()};T.set(n,t);const e=j(n);window.addEventListener("resize",t,{passive:!0}),n!==document.documentElement&&k.set(n,(l=t,v(f=n)?c(f):r(f,l))),e.addEventListener("scroll",t,{passive:!0})}var f,l;const a=T.get(n),u=requestAnimationFrame(a);return()=>{var e;"function"!=typeof t&&t.stop(),cancelAnimationFrame(u);const o=P.get(n);if(!o)return;if(o.delete(s),o.size)return;const r=T.get(n);T.delete(n),r&&(j(n).removeEventListener("scroll",r),null===(e=k.get(n))||void 0===e||e(),window.removeEventListener("resize",r))}}export{q as scroll};
