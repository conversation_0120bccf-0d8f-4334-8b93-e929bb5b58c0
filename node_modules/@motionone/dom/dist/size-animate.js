const t=(t,e,n)=>Math.min(Math.max(n,t),e),e={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"},n=t=>"number"==typeof t,i=t=>Array.isArray(t)&&!n(t[0]),a=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};const s=(t,e,n)=>-n*t+n*e+t,r=()=>{},o=t=>t,l=(t,e,n)=>e-t==0?1:(n-t)/(e-t);function c(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const a=l(0,e,i);t.push(s(n,1,a))}}function u(e,n=function(t){const e=[0];return c(e,t-1),e}(e.length),r=o){const u=e.length,h=u-n.length;return h>0&&c(n,h),o=>{let c=0;for(;c<u-2&&!(o<n[c+1]);c++);let h=t(0,1,l(n[c],n[c+1],o));const d=function(t,e){return i(t)?t[a(0,t.length,e)]:t}(r,c);return h=d(h),s(e[c],e[c+1],h)}}const h=t=>Array.isArray(t)&&n(t[0]),d=t=>"object"==typeof t&&Boolean(t.createAnimation),f=t=>"function"==typeof t,m=t=>"string"==typeof t,p={ms:t=>1e3*t,s:t=>t/1e3},y=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,g=1e-7,v=12;function w(t,e,n,i){if(t===e&&n===i)return o;const a=e=>function(t,e,n,i,a){let s,r,o=0;do{r=e+(n-e)/2,s=y(r,i,a)-t,s>0?n=r:e=r}while(Math.abs(s)>g&&++o<v);return r}(e,0,1,t,n);return t=>0===t||1===t?t:y(a(t),e,i)}const T={ease:w(.25,.1,.25,1),"ease-in":w(.42,0,1,1),"ease-in-out":w(.42,0,.58,1),"ease-out":w(0,0,.58,1)},S=/\((.*?)\)/;function D(e){if(f(e))return e;if(h(e))return w(...e);const n=T[e];if(n)return n;if(e.startsWith("steps")){const n=S.exec(e);if(n){const e=n[1].split(",");return((e,n="end")=>i=>{const a=(i="end"===n?Math.min(i,.999):Math.max(i,.001))*e,s="end"===n?Math.floor(a):Math.ceil(a);return t(0,1,s/e)})(parseFloat(e[0]),e[1].trim())}}return o}var b=function(){};"production"!==process.env.NODE_ENV&&(b=function(t,e){if(!t)throw new Error(e)});class A{setAnimation(t){this.animation=t,null==t||t.finished.then((()=>this.clearAnimation())).catch((()=>{}))}clearAnimation(){this.animation=this.generator=void 0}}const x=new WeakMap;function O(t){return x.has(t)||x.set(t,{transforms:[],values:new Map}),x.get(t)}const k=["","X","Y","Z"],E={x:"translateX",y:"translateY",z:"translateZ"},M={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:t=>t+"deg"},R={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:t=>t+"px"},rotate:M,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:o},skew:M},P=new Map,j=t=>`--motion-${t}`,V=["x","y","z"];["translate","scale","rotate","skew"].forEach((t=>{k.forEach((e=>{V.push(t+e),P.set(j(t+e),R[t])}))}));const $=(t,e)=>V.indexOf(t)-V.indexOf(e),q=new Set(V),F=t=>q.has(t),N=(t,e)=>{E[e]&&(e=E[e]);const{transforms:n}=O(t);var i,a;a=e,-1===(i=n).indexOf(a)&&i.push(a),t.style.transform=U(n)},U=t=>t.sort($).reduce(_,"").trim(),_=(t,e)=>`${t} ${e}(var(${j(e)}))`,B=t=>t.startsWith("--"),C=new Set;const I=(t,e)=>document.createElement("div").animate(t,e),z={cssRegisterProperty:()=>"undefined"!=typeof CSS&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{I({opacity:[1]})}catch(t){return!1}return!0},finished:()=>Boolean(I({opacity:[0,1]},{duration:.001}).finished),linearEasing:()=>{try{I({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}},W={},K={};for(const t in z)K[t]=()=>(void 0===W[t]&&(W[t]=z[t]()),W[t]);const X=(t,n)=>f(t)?K.linearEasing()?`linear(${((t,e)=>{let n="";const i=Math.round(e/.015);for(let e=0;e<i;e++)n+=t(l(0,i-1,e))+", ";return n.substring(0,n.length-2)})(t,n)})`:e.easing:h(t)?Y(t):t,Y=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`;const Z=t=>Array.isArray(t)?t:[t];function L(t){return E[t]&&(t=E[t]),F(t)?j(t):t}const G={get:(t,e)=>{e=L(e);let n=B(e)?t.style.getPropertyValue(e):getComputedStyle(t)[e];if(!n&&0!==n){const t=P.get(e);t&&(n=t.initialValue)}return n},set:(t,e,n)=>{e=L(e),B(e)?t.style.setProperty(e,n):t.style[e]=n}};function H(t,e=!0){if(t&&"finished"!==t.playState)try{t.stop?t.stop():(e&&t.commitStyles(),t.cancel())}catch(t){}}function J(t,a,s,l={},c){const u=window.__MOTION_DEV_TOOLS_RECORD,h=!1!==l.record&&u;let y,{duration:g=e.duration,delay:v=e.delay,endDelay:w=e.endDelay,repeat:T=e.repeat,easing:S=e.easing,persist:D=!1,direction:b,offset:x,allowWebkitAcceleration:k=!1,autoplay:E=!0}=l;const M=O(t),R=F(a);let j=K.waapi();R&&N(t,a);const V=L(a),$=function(t,e){return t.has(e)||t.set(e,new A),t.get(e)}(M.values,V),q=P.get(V);return H($.animation,!(d(S)&&$.generator)&&!1!==l.record),()=>{const e=()=>{var e,n;return null!==(n=null!==(e=G.get(t,V))&&void 0!==e?e:null==q?void 0:q.initialValue)&&void 0!==n?n:0};let A=function(t,e){for(let n=0;n<t.length;n++)null===t[n]&&(t[n]=n?t[n-1]:e());return t}(Z(s),e);const O=function(t,e){var n;let i=(null==e?void 0:e.toDefaultUnit)||o;const a=t[t.length-1];if(m(a)){const t=(null===(n=a.match(/(-?[\d.]+)([a-z%]*)/))||void 0===n?void 0:n[2])||"";t&&(i=e=>e+t)}return i}(A,q);if(d(S)){const t=S.createAnimation(A,"opacity"!==a,e,V,$);S=t.easing,A=t.keyframes||A,g=t.duration||g}if(B(V)&&(K.cssRegisterProperty()?function(t){if(!C.has(t)){C.add(t);try{const{syntax:e,initialValue:n}=P.has(t)?P.get(t):{};CSS.registerProperty({name:t,inherits:!1,syntax:e,initialValue:n})}catch(t){}}}(V):j=!1),R&&!K.linearEasing()&&(f(S)||i(S)&&S.some(f))&&(j=!1),j){q&&(A=A.map((t=>n(t)?q.toDefaultUnit(t):t))),1!==A.length||K.partialKeyframes()&&!h||A.unshift(e());const a={delay:p.ms(v),duration:p.ms(g),endDelay:p.ms(w),easing:i(S)?void 0:X(S,g),direction:b,iterations:T+1,fill:"both"};y=t.animate({[V]:A,offset:x,easing:i(S)?S.map((t=>X(t,g))):void 0},a),y.finished||(y.finished=new Promise(((t,e)=>{y.onfinish=t,y.oncancel=e})));const s=A[A.length-1];y.finished.then((()=>{D||(G.set(t,V,s),y.cancel())})).catch(r),k||(y.playbackRate=1.000001)}else if(c&&R)A=A.map((t=>"string"==typeof t?parseFloat(t):t)),1===A.length&&A.unshift(parseFloat(e())),y=new c((e=>{G.set(t,V,O?O(e):e)}),A,Object.assign(Object.assign({},l),{duration:g,easing:S}));else{const e=A[A.length-1];G.set(t,V,q&&n(e)?q.toDefaultUnit(e):e)}return h&&u(t,a,A,{duration:g,delay:v,easing:S,repeat:T,offset:x},"motion-one"),$.setAnimation(y),y&&!E&&y.pause(),y}}const Q=(t,e)=>t[e]?Object.assign(Object.assign({},t),t[e]):Object.assign({},t);const tt=t=>t(),et={get:(t,e)=>{const n=t.animations[0];switch(e){case"duration":return t.duration;case"currentTime":return p.s((null==n?void 0:n[e])||0);case"playbackRate":case"playState":return null==n?void 0:n[e];case"finished":return t.finished||(t.finished=Promise.all(t.animations.map(nt)).catch(r)),t.finished;case"stop":return()=>{t.animations.forEach((t=>H(t)))};case"forEachNative":return e=>{t.animations.forEach((n=>e(n,t)))};default:return void 0===(null==n?void 0:n[e])?void 0:()=>t.animations.forEach((t=>t[e]()))}},set:(t,e,n)=>{switch(e){case"currentTime":n=p.ms(n);case"playbackRate":for(let i=0;i<t.animations.length;i++)t.animations[i][e]=n;return!0}return!1}},nt=t=>t.finished;function it(t,e,n){return f(t)?t(e,n):t}const at=(st=class{constructor(t,n=[0,1],{easing:a,duration:s=e.duration,delay:r=e.delay,endDelay:l=e.endDelay,repeat:c=e.repeat,offset:h,direction:f="normal",autoplay:m=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=o,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),a=a||e.easing,d(a)){const t=a.createAnimation(n);a=t.easing,n=t.keyframes||n,s=t.duration||s}this.repeat=c,this.easing=i(a)?o:D(a),this.updateDuration(s);const p=u(n,h,i(a)?a.map(D):o);this.tick=e=>{var n;let i=0;i=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=i,i/=1e3,i=Math.max(i-r,0),"finished"===this.playState&&void 0===this.pauseTime&&(i=this.totalDuration);const a=i/this.duration;let s=Math.floor(a),o=a%1;!o&&a>=1&&(o=1),1===o&&s--;const c=s%2;("reverse"===f||"alternate"===f&&c||"alternate-reverse"===f&&!c)&&(o=1-o);const u=i>=this.totalDuration?1:Math.min(o,1),h=p(this.easing(u));t(h),void 0===this.pauseTime&&("finished"===this.playState||i>=this.totalDuration+l)?(this.playState="finished",null===(n=this.resolve)||void 0===n||n.call(this,h)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},m&&this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}},function(t,n,i={}){const a=(t=function(t,e){var n;return"string"==typeof t?e?(null!==(n=e[t])&&void 0!==n||(e[t]=document.querySelectorAll(t)),t=e[t]):t=document.querySelectorAll(t):t instanceof Element&&(t=[t]),Array.from(t||[])}(t)).length;b(Boolean(a),"No valid element provided."),b(Boolean(n),"No keyframes defined.");const s=[];for(let e=0;e<a;e++){const r=t[e];for(const t in n){const o=Q(i,t);o.delay=it(o.delay,e,a);const l=J(r,t,n[t],o,st);s.push(l)}}return((t,n,i=e.duration)=>new Proxy({animations:t.map(tt).filter(Boolean),duration:i,options:n},et))(s,i,i.duration)});var st;export{at as animate};
