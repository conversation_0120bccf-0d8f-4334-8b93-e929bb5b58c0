[{"key": "group_goat_events_block", "title": "GOAT Events Block", "fields": [{"key": "field_events_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Optional anchor ID for navigation", "required": 0}, {"key": "field_events_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "instructions": "Section subtitle", "required": 0}, {"key": "field_events_title", "label": "Title", "name": "title", "type": "text", "instructions": "Section title", "required": 0}, {"key": "field_events_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Section description", "required": 0, "rows": 4}, {"key": "field_events_show_filters", "label": "Show Event Filters", "name": "show_event_filters", "type": "true_false", "instructions": "Show upcoming/past event filters", "required": 0, "default_value": 1, "ui": 1}, {"key": "field_events_per_page", "label": "Events Per Page", "name": "events_per_page", "type": "number", "instructions": "Number of events to show", "required": 0, "default_value": 6, "min": 1, "max": 20}, {"key": "field_events_load_more", "label": "Enable Load More", "name": "load_more_enabled", "type": "true_false", "instructions": "Enable load more functionality", "required": 0, "default_value": 0, "ui": 1}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-events-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_three_events_block", "title": "GOAT Three Events Block", "fields": [{"key": "field_three_events_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Optional anchor ID for navigation", "required": 0}, {"key": "field_three_events_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "instructions": "Section subtitle", "required": 0}, {"key": "field_three_events_title", "label": "Title", "name": "title", "type": "text", "instructions": "Section title", "required": 0}, {"key": "field_three_events_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Section description", "required": 0, "rows": 4}, {"key": "field_three_events_selected_events", "label": "Selected Events", "name": "selected_events", "type": "relationship", "instructions": "Select specific events to show (leave empty to show latest 3)", "required": 0, "post_type": ["event"], "taxonomy": [], "filters": ["search"], "elements": ["featured_image"], "min": 0, "max": 3, "return_format": "object"}, {"key": "field_three_events_view_all_button", "label": "View All Events Button", "name": "view_all_events_button", "type": "link", "instructions": "Link to full events page", "required": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-three-events-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}, {"key": "group_goat_contact_block", "title": "GOAT Contact Block", "fields": [{"key": "field_contact_anchor", "label": "<PERSON><PERSON>", "name": "anchor", "type": "text", "instructions": "Optional anchor ID for navigation", "required": 0}, {"key": "field_contact_subtitle", "label": "Subtitle", "name": "subtitle", "type": "text", "instructions": "Section subtitle", "required": 0}, {"key": "field_contact_title", "label": "Title", "name": "title", "type": "text", "instructions": "Section title", "required": 0}, {"key": "field_contact_description", "label": "Description", "name": "description", "type": "textarea", "instructions": "Section description", "required": 0, "rows": 4}, {"key": "field_contact_email", "label": "Contact Email", "name": "contact_email", "type": "email", "instructions": "Contact email address", "required": 0}, {"key": "field_contact_phone", "label": "Contact Phone", "name": "contact_phone", "type": "text", "instructions": "Contact phone number", "required": 0}, {"key": "field_contact_address", "label": "Contact Address", "name": "contact_address", "type": "textarea", "instructions": "Contact address", "required": 0, "rows": 3}, {"key": "field_contact_hours", "label": "Contact Hours", "name": "contact_hours", "type": "textarea", "instructions": "Business hours", "required": 0, "rows": 3}, {"key": "field_contact_social_section", "label": "Show Social Media Section", "name": "social_media_section", "type": "true_false", "instructions": "Show social media links", "required": 0, "default_value": 1, "ui": 1}, {"key": "field_contact_social_facebook", "label": "Facebook URL", "name": "social_facebook", "type": "url", "instructions": "Facebook page URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_twitter", "label": "Twitter URL", "name": "social_twitter", "type": "url", "instructions": "Twitter profile URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_instagram", "label": "Instagram URL", "name": "social_instagram", "type": "url", "instructions": "Instagram profile URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_discord", "label": "Discord URL", "name": "social_discord", "type": "url", "instructions": "Discord server URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_twitch", "label": "Twitch URL", "name": "social_twitch", "type": "url", "instructions": "Twitch channel URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_youtube", "label": "YouTube URL", "name": "social_youtube", "type": "url", "instructions": "YouTube channel URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_social_tiktok", "label": "TikTok URL", "name": "social_tiktok", "type": "url", "instructions": "TikTok profile URL", "required": 0, "conditional_logic": [[{"field": "field_contact_social_section", "operator": "==", "value": "1"}]]}, {"key": "field_contact_form_title", "label": "Form Title", "name": "form_title", "type": "text", "instructions": "Contact form title", "required": 0}, {"key": "field_contact_form_shortcode", "label": "Contact Form Shortcode", "name": "contact_form_shortcode", "type": "text", "instructions": "Contact form shortcode (e.g., Contact Form 7)", "required": 0}], "location": [[{"param": "block", "operator": "==", "value": "acf/goat-contact-block"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label"}]